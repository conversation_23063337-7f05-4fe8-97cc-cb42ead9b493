# 前端下单逻辑修改总结

## 修改概述

根据后端需求，前端下单逻辑已经修改为支持两种下单方式：

1. **食谱下单**：传递 `recipe_id` 到订单项和订单级别
2. **直接下单**：不传递 `recipe_id`（保持为 null）

## 具体修改

### 1. 食谱下单逻辑修改

**文件**: `web/src/views/food-menu/index.vue`

**修改内容**:
- 在 `submitOrder()` 函数中，为每个订单项添加 `recipe_id`
- 在订单级别也添加 `recipeId` 字段
- 使用专门的食谱下单API接口

**修改前**:
```javascript
const orderItems = currentMenu.value.food_stuff_list.map(item => {
  const totalRequired = calculateRequiredAmount(item)
  return {
    id: item.food_stuff_id,
    name: item.food_stuff_name,
    quantity: totalRequired,
    unitName: item.food_stuff_unit
  }
})

const orderData = {
  orderItems: orderItems,
  orderRemark: orderRemark
}

await api.createOrder(orderData)
```

**修改后**:
```javascript
const orderItems = currentMenu.value.food_stuff_list.map(item => {
  const totalRequired = calculateRequiredAmount(item)
  return {
    id: item.food_stuff_id,
    name: item.food_stuff_name,
    quantity: totalRequired,
    unitName: item.food_stuff_unit,
    recipe_id: currentMenu.value.id // 添加食谱ID
  }
})

const orderData = {
  orderItems: orderItems,
  orderRemark: orderRemark,
  recipeId: currentMenu.value.id // 在订单级别也添加recipe_id
}

await api.createOrderFromRecipe(orderData) // 使用专门的食谱下单接口
```

### 2. 直接下单逻辑保持不变

**文件**: `web/src/views/food-stuff/food-stuff/index.vue`

**保持原有逻辑**:
```javascript
const orderData = {
  orderItems: cartStore.cartItems.map(item => ({
    id: item.id,
    name: item.name,
    type: item.type,
    quantity: item.quantity,
    unitId: item.unitId,
    unitName: item.unitName,
    supplierId: item.supplierId,
    supplierName: item.supplierName
    // 注意：这里没有 recipe_id，保持为 null
  })),
  orderRemark: '从购物车直接下单'
}

await api.createDirectOrder(orderData) // 使用直接下单接口
```

### 3. API接口添加

**文件**: `web/src/api/index.js`

**新增接口**:
```javascript
createOrderFromRecipe: (data) => request.post('/orders/create-from-recipe', data), // 食谱下单接口（检查库存）
```

## 数据流说明

### 食谱下单流程:
1. 用户在食谱页面点击"下单"按钮
2. 前端收集食谱中的所有食材信息
3. 为每个食材添加 `recipe_id: currentMenu.value.id`
4. 调用 `/orders/create-from-recipe` 接口
5. 后端处理时会：
   - 检查库存并按先进先出原则扣除
   - 在库存记录中保存 `order_id` 和 `recipe_id`
   - 创建订单时包含 `recipe_id` 信息

### 直接下单流程:
1. 用户在食材管理页面添加食材到购物车
2. 点击购物车中的"确认下单"按钮
3. 前端收集购物车中的食材信息（不包含 `recipe_id`）
4. 调用 `/orders/create-direct` 接口
5. 后端处理时会：
   - 不检查库存，直接创建订单
   - 在库存记录中保存 `order_id`，`recipe_id` 为 null

## 库存管理逻辑

### 后端库存处理:
- **入库时**: 每个订单创建独立的库存记录，包含 `order_id` 和 `recipe_id`
- **扣除时**: 按先进先出原则扣除，优先使用最早入库的食材
- **显示时**: 前端显示时合并相同供应商的库存记录
- **删除时**: 库存为0时自动删除记录

### 数据追溯:
- 通过 `order_id` 可以追溯库存来源订单
- 通过 `recipe_id` 可以追溯库存关联的食谱
- 支持库存变化记录的完整追踪

## 测试建议

1. **食谱下单测试**:
   - 创建食谱并添加菜品
   - 点击下单，检查订单项是否包含 `recipe_id`
   - 验证库存记录是否正确保存 `recipe_id`

2. **直接下单测试**:
   - 在食材管理页面添加食材到购物车
   - 确认下单，检查订单项不包含 `recipe_id`
   - 验证库存记录中 `recipe_id` 为 null

3. **库存扣除测试**:
   - 创建多个订单产生相同食材的库存
   - 进行下单操作，验证是否按先进先出原则扣除
   - 检查库存为0时是否自动删除记录
