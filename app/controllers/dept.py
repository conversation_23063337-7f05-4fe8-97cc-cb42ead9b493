from datetime import datetime
import secrets
import string
from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.controllers.user import user_controller
from app.core.ctx import CTX_USER_ID
from app.models.admin import Cooperators, Dept, DeptClosure, DeptType, User
from app.schemas.depts import Dept<PERSON>reate, DeptUpdate
from app.controllers.utils import get_school_id


class DeptController(CRUDBase[Dept, DeptCreate, DeptUpdate]):
    def __init__(self):
        super().__init__(model=Dept)

    async def get_dept_tree(self, name=None, phone=None, type=None):
        q = Q()
        # 获取所有未被软删除的部门
        q &= Q(is_deleted=False)
        if name:
            q &= Q(name__contains=name)
        if phone:
            q &= Q(phone__contains=phone)
        if type:
            q &= Q(type=type)
        all_depts = await self.model.filter(q).order_by("order")

        # 辅助函数，用于递归构建部门树
        def build_tree(parent_id):
            return [
                {
                    "id": dept.id,
                    "name": dept.name,
                    "type": dept.type,
                    "phone": dept.phone,
                    "email": dept.email,
                    "address": dept.address,
                    "desc": dept.desc,
                    "token": dept.token,
                    "order": dept.order,
                    "parent_id": dept.parent_id,
                    "children": build_tree(dept.id),  # 递归构建子部门
                }
                for dept in all_depts
                if dept.parent_id == parent_id
            ]

        # 从顶级部门（parent_id=0）开始构建部门树
        dept_tree = build_tree(0)
        return dept_tree

    async def get_dept_info(self):
        pass

    async def update_dept_closure(self, obj: Dept):
        parent_depts = await DeptClosure.filter(descendant=obj.parent_id)
        for i in parent_depts:
            print(i.ancestor, i.descendant)
        dept_closure_objs: list[DeptClosure] = []
        # 插入父级关系
        for item in parent_depts:
            dept_closure_objs.append(DeptClosure(ancestor=item.ancestor, descendant=obj.id, level=item.level + 1))
        # 插入自身x
        dept_closure_objs.append(DeptClosure(ancestor=obj.id, descendant=obj.id, level=0))
        # 创建关系
        await DeptClosure.bulk_create(dept_closure_objs)

    @atomic()
    async def create_dept(self, obj_in: DeptCreate):
        # 创建
        if obj_in.parent_id != 0:
            await self.get(id=obj_in.parent_id)
        new_obj = await self.create(obj_in=obj_in)
        await self.update_dept_closure(new_obj)

    @atomic()
    async def update_dept(self, obj_in: DeptUpdate):
        dept_obj = await self.get(id=obj_in.id)
        # 更新部门关系
        if dept_obj.parent_id != obj_in.parent_id:
            await DeptClosure.filter(ancestor=dept_obj.id).delete()
            await DeptClosure.filter(descendant=dept_obj.id).delete()
            await self.update_dept_closure(dept_obj)
        # 更新部门信息
        dept_obj.update_from_dict(obj_in.model_dump(exclude_unset=True))
        await dept_obj.save()

    @atomic()
    async def delete_dept(self, dept_id: int):
        # 删除部门
        obj = await self.get(id=dept_id)
        obj.is_deleted = True
        await obj.save()
        # 删除关系
        await DeptClosure.filter(descendant=dept_id).delete()

    @atomic()
    async def add_supplier(self, supplier_id: int):
        school_id = await get_school_id()
        # 检查供应商是否已经存在
        existing_cooperation = await Cooperators.filter(school_id=school_id, supplier_id=supplier_id).first()
        if existing_cooperation:
            raise Exception("供应商已经存在")

        # 创建合作关系
        await Cooperators.create(school_id=school_id, supplier_id=supplier_id)

    async def filter_suppliers(self, name=None):
        q = Q()
        q &= Q(is_deleted=False)
        q &= Q(type=DeptType.SUPPLY)
        if name:
            q &= Q(name__contains=name)
        suppliers = await self.model.filter(q).order_by("order")
        return [{"label": supplier.name, "value": supplier.id} for supplier in suppliers]

    async def get_suppliers(self, name=None):
        school_id = await get_school_id()

        q = Q()
        # 获取所有未被软删除的供应商
        q &= Q(is_deleted=False)
        q &= Q(type=DeptType.SUPPLY)
        if name:
            q &= Q(name__contains=name)
            # 获取供应商合作关系
        cooperators = await Cooperators.filter(school_id=school_id).values_list("supplier_id", flat=True)
        q &= Q(id__in=cooperators)
        suppliers = await self.model.filter(q).order_by("order")
        # suppliers = await self.model.filter(q).order_by("order")
        result = []
        for supplier in suppliers:
            result.append({
                "id": supplier.id,
                "name": supplier.name,
                "phone": supplier.phone,
                "address": supplier.address,
                "email": supplier.email,
                "desc": supplier.desc
            })
        return result


    @atomic()
    async def remove_supplier(self, supplier_id: int):
        """
        移除供应商

        Args:
            supplier_id: 供应商ID
        """
        school_id = await get_school_id()

        # 检查供应商是否存在
        existing_cooperation = await Cooperators.filter(school_id=school_id, supplier_id=supplier_id).first()
        if not existing_cooperation:
            raise Exception("供应商不存在")

        # 删除合作关系
        await existing_cooperation.delete()


    @atomic()
    async def generate_school_token(self, school_id: int):
        """
        生成学校token
        """
        # 检查学校是否存在
        school = await self.get(id=school_id)
        if not school:
            raise Exception("学校不存在")

        # 检查是否是超级管理员
        user = await User.filter(id=CTX_USER_ID.get()).first()
        if not user:
            raise Exception("用户不存在")
        if user.is_superuser:
            token = await self._generate_unique_token()
            school.token = token
            await school.save()
            return token
        else:
            raise Exception("非超级管理员不能生成token")
            

    async def _generate_unique_token(self):
        """
        递归生成不重复的token

        Returns:
            str: 生成的唯一token
        """
        all_characters = string.ascii_letters + string.digits
        token = ''.join(secrets.choice(all_characters) for _ in range(5))
        if await self.model.filter(token=token).first():
            # 如果token已存在，递归调用生成新的token
            return await self._generate_unique_token()
        return token

    async def get_class_list(self, name=None):
        """
        获取班级列表，只有当前用户的部门类型是学校才能查看班级列表

        Args:
            name: 班级名称，用于筛选

        Returns:
            list: 班级列表
        """
        # 获取当前用户所属部门
        user_id = CTX_USER_ID.get()
        user_obj = await user_controller.get(id=user_id)
        if not user_obj or not user_obj.dept_id:
            raise Exception("用户没有所属部门")

        # 获取用户所属部门信息
        user_dept = await Dept.filter(id=user_obj.dept_id).first()

        # 检查部门类型是否为学校
        if user_dept.type != DeptType.SCHOOL:
            raise Exception("只有学校才能查看班级列表")

        # 获取学校ID
        school_id = user_dept.id

        # 构建查询条件
        q = Q()
        q &= Q(is_deleted=False)
        q &= Q(parent_id=school_id)  # 查询该学校下的班级
        if name:
            q &= Q(name__contains=name)

        # 获取班级列表
        classes = await self.model.filter(q).order_by("order")

        # 构建返回结果
        result = []
        for class_info in classes:
            result.append({
                "id": class_info.id,
                "name": class_info.name,
                "desc": class_info.desc,
                "order": class_info.order,
                "parent_id": class_info.parent_id,
                "student_count": class_info.student_count
            })

        return result

    @atomic()
    async def update_student_count(self, token: str, student_count: int):
        """
        更新班级学生数量

        Args:
            class_id: 班级ID
            student_count: 学生数量

        Returns:
            Dept: 更新后的班级对象
        """
        # 检查班级是否存在
        class_obj = await self.model.filter(token=token).first()
        if not class_obj:
            raise Exception("班级不存在")

        # 检查是否是班级（有父部门）
        if class_obj.parent_id == 0:
            raise Exception("只能更新班级的学生数量")

        # 更新学生数量
        class_obj.student_count = student_count
        await class_obj.save()

        return class_obj


    @atomic()
    async def update_student_count_by_class_id(self, class_id: int, student_count: int):
        """
        更新班级学生数量

        Args:
            class_id: 班级ID
            student_count: 学生数量

        Returns:
            Dept: 更新后的班级对象
        """
        # 检查班级是否存在
        class_obj = await self.model.filter(id=class_id).first()
        if not class_obj:
            raise Exception("班级不存在")

        # 检查是否是班级（有父部门）
        if class_obj.parent_id == 0:
            raise Exception("只能更新班级的学生数量")

        # 更新学生数量
        class_obj.student_count = student_count
        await class_obj.save()

        return class_obj

    @atomic()
    async def class_login(self, phone: str):
        """
        班级登录

        Args:
            phone: 班主任手机号

        Returns:
            Dept: 登录后的班级对象
        """
        # 检查班主任手机号是否存在
        class_obj = await self.model.filter(phone=phone).first()
        if not class_obj:
            raise Exception("班主任手机号不存在")

        # 检查是否是班级（有父部门）
        if class_obj.parent_id == 0:
            raise Exception("只能班级班主任登录")

        class_obj.token = await self._generate_unique_token()
        await class_obj.save()

        return class_obj

    async def get_total_student_count(self):
        """
        获取当前学校的学生总数

        Returns:
            int: 学生总数
        """
        # 获取当前用户所属学校ID
        school_id = await get_school_id()

        # 获取该学校下所有班级
        q = Q()
        q &= Q(is_deleted=False)
        q &= Q(parent_id=school_id)  # 查询该学校下的班级

        # 获取班级列表
        classes = await self.model.filter(q)

        # 计算学生总数
        total_student_count = sum(class_info.student_count for class_info in classes)

        return total_student_count

dept_controller = DeptController()
