from tortoise.expressions import Q
from tortoise.transactions import atomic
from datetime import date
from typing import List, Dict, Any
from collections import defaultdict

from app.core.crud import CRUDBase
from app.models.food_stuff import DailyCostReport, FoodStuffStore, FoodStuff, Unit
from app.models.food_menu import Recipe, RecipeMeal
from app.models.orders import Order, OrderStatus
from app.schemas.daily_cost_report import DailyCostReportCreate, DailyCostReportUpdate, DailyCostReportSearch, DailyCostReportGenerate
from app.controllers.utils import get_school_id


class DailyCostReportController(CRUDBase[DailyCostReport, DailyCostReportCreate, DailyCostReportUpdate]):
    def __init__(self):
        super().__init__(model=DailyCostReport)

    async def get_report_list(self, search: DailyCostReportSearch):
        """获取日成本核算单列表"""
        try:
            school_id = await get_school_id()
        except Exception:
            return {
                "total": 0,
                "data": []
            }

        q = Q(school_id=school_id)
        
        if search.report_date:
            q &= Q(report_date=search.report_date)
        
        if search.start_date and search.end_date:
            q &= Q(report_date__gte=search.start_date, report_date__lte=search.end_date)
        elif search.start_date:
            q &= Q(report_date__gte=search.start_date)
        elif search.end_date:
            q &= Q(report_date__lte=search.end_date)
            
        if search.status:
            q &= Q(status=search.status)

        total = await self.model.filter(q).count()
        
        offset = (search.current - 1) * search.size
        reports = await self.model.filter(q).order_by("-report_date").offset(offset).limit(search.size)
        
        # 获取关联的食谱信息
        data = []
        for report in reports:
            report_dict = await report.to_dict()
            
            # 获取食谱信息
            if report.recipe_id:
                recipe = await Recipe.get_or_none(id=report.recipe_id)
                if recipe:
                    report_dict['recipe_name'] = recipe.recipe_name
                else:
                    report_dict['recipe_name'] = None
            else:
                report_dict['recipe_name'] = None
                
            data.append(report_dict)

        return {
            "total": total,
            "data": data
        }

    async def get_report_detail(self, report_id: int):
        """获取日成本核算单详情"""
        school_id = await get_school_id()
        
        report = await self.model.filter(id=report_id, school_id=school_id).first()
        if not report:
            raise ValueError("核算单不存在或不属于当前学校")
            
        report_dict = await report.to_dict()
        
        # 获取食谱信息
        if report.recipe_id:
            recipe = await Recipe.get_or_none(id=report.recipe_id)
            if recipe:
                report_dict['recipe_name'] = recipe.recipe_name
                # 获取餐次信息
                meals = await RecipeMeal.filter(recipe=recipe).all()
                report_dict['meals'] = []
                for meal in meals:
                    meal_dict = await meal.to_dict()
                    report_dict['meals'].append(meal_dict)
            else:
                report_dict['recipe_name'] = None
                report_dict['meals'] = []
        else:
            report_dict['recipe_name'] = None
            report_dict['meals'] = []
            
        return report_dict

    @atomic()
    async def generate_report(self, obj_in: DailyCostReportGenerate):
        """生成日成本核算单"""
        school_id = await get_school_id()
        
        # 检查是否已存在该日期的核算单
        existing_report = await self.model.filter(
            report_date=obj_in.report_date,
            school_id=school_id
        ).first()
        
        if existing_report:
            raise ValueError(f"日期 {obj_in.report_date} 的核算单已存在")
        
        # 获取食谱信息
        recipe_id = obj_in.recipe_id
        if not recipe_id:
            # 自动查找当日食谱
            recipe = await Recipe.filter(
                recipe_date=obj_in.report_date,
                school_id=school_id,
                is_active=True
            ).first()
            if recipe:
                recipe_id = recipe.id
        
        if not recipe_id:
            raise ValueError(f"未找到日期 {obj_in.report_date} 的食谱")
        
        # 获取当日所有相关的库存消耗和订单消耗
        cost_details = await self._calculate_daily_cost(obj_in.report_date, school_id, recipe_id)
        
        # 计算总成本
        total_cost = 0.0
        inventory_cost = 0.0
        order_cost = 0.0

        for detail in cost_details:
            if 'inventory_consume' in detail and detail['inventory_consume']:
                inventory_cost += float(detail['inventory_consume'].get('cost', 0.0))
            if 'order_consume' in detail and detail['order_consume']:
                order_cost += float(detail['order_consume'].get('cost', 0.0))

        total_cost = inventory_cost + order_cost
        
        # 创建核算单
        report = DailyCostReport(
            report_date=obj_in.report_date,
            school_id=school_id,
            recipe_id=recipe_id,
            total_cost=total_cost,
            inventory_cost=inventory_cost,
            order_cost=order_cost,
            cost_details=cost_details,
            status="draft"
        )
        await report.save()
        
        return report

    async def _calculate_daily_cost(self, report_date: date, school_id: int, recipe_id: int) -> List[Dict[str, Any]]:
        """计算指定日期的成本明细"""
        # 获取当日所有库存变化记录（消耗类型）
        from app.models.food_stuff import FoodStuffStoreRecord

        # 查找当日的库存消耗记录
        from datetime import datetime
        start_of_day = datetime.combine(report_date, datetime.min.time())
        end_of_day = datetime.combine(report_date, datetime.max.time())

        store_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            created_at__gte=start_of_day,
            created_at__lte=end_of_day,
            operation_type__in=["下单库存消耗", "食谱库存消耗"]
        ).all()

        # 查找当日入库后立即消耗的订单（通过recipe_id关联）
        orders = await Order.filter(
            school_id=school_id,
            order_confirm_date__gte=start_of_day,
            order_confirm_date__lte=end_of_day,
            order_status=OrderStatus.CONFIRMED
        ).all()

        # 按食材ID分组统计
        food_stuff_costs = defaultdict(lambda: {
            'food_stuff_id': 0,
            'food_stuff_name': '',
            'unit_name': '',
            'inventory_consume': {'quantity': 0.0, 'cost': 0.0, 'orders': []},
            'order_consume': {'quantity': 0.0, 'cost': 0.0, 'orders': []}
        })

        # 处理库存消耗
        for record in store_records:
            for food_data in record.food_stuff_data:
                food_stuff_id = food_data.get('food_stuff_id')
                if food_stuff_id:
                    change_count = abs(food_data.get('change_count', 0))  # 消耗为负数，取绝对值

                    # 获取该食材在当日的平均价格（从库存记录中）
                    avg_price = await self._get_food_stuff_avg_price(food_stuff_id, school_id, report_date)
                    cost = change_count * avg_price

                    food_stuff_costs[food_stuff_id]['food_stuff_id'] = food_stuff_id
                    food_stuff_costs[food_stuff_id]['food_stuff_name'] = food_data.get('food_stuff_name', '')
                    food_stuff_costs[food_stuff_id]['unit_name'] = food_data.get('unit_name', '')
                    food_stuff_costs[food_stuff_id]['inventory_consume']['quantity'] += change_count
                    food_stuff_costs[food_stuff_id]['inventory_consume']['cost'] += cost

        # 处理订单消耗（入库后立即消耗）
        for order in orders:
            for item in order.order_items:
                if item.get('recipe_id') == recipe_id and item.get('immediate_consume'):
                    food_stuff_id = item.get('id')
                    quantity = float(item.get('consume_quantity', 0))
                    price = float(item.get('price', 0))
                    cost = quantity * price

                    # 获取食材信息
                    food_stuff = await FoodStuff.get_or_none(id=food_stuff_id)
                    unit = await Unit.get_or_none(id=food_stuff.food_stuff_unit_id) if food_stuff else None

                    food_stuff_costs[food_stuff_id]['food_stuff_id'] = food_stuff_id
                    food_stuff_costs[food_stuff_id]['food_stuff_name'] = food_stuff.food_stuff_name if food_stuff else ''
                    food_stuff_costs[food_stuff_id]['unit_name'] = unit.unit_name if unit else ''
                    food_stuff_costs[food_stuff_id]['order_consume']['quantity'] += quantity
                    food_stuff_costs[food_stuff_id]['order_consume']['cost'] += cost
                    food_stuff_costs[food_stuff_id]['order_consume']['orders'].append({
                        'order_id': order.id,
                        'order_number': order.order_number,
                        'quantity': quantity,
                        'price': price
                    })

        # 过滤掉没有消耗的食材
        result = []
        for food_stuff_id, data in food_stuff_costs.items():
            if data['inventory_consume']['quantity'] > 0 or data['order_consume']['quantity'] > 0:
                result.append(data)

        return result

    async def _get_food_stuff_avg_price(self, food_stuff_id: int, school_id: int, report_date: date) -> float:
        """获取食材在指定日期的平均价格"""
        # 获取当日及之前的库存记录，按时间倒序
        stores = await FoodStuffStore.filter(
            food_stuff_id=food_stuff_id,
            school_id=school_id,
            created_at__date__lte=report_date
        ).order_by('-created_at').limit(10)  # 取最近10条记录计算平均价格
        
        if not stores:
            return 0.0
        
        total_cost = sum(store.price for store in stores)
        return total_cost / len(stores)

    @atomic()
    async def confirm_report(self, report_id: int):
        """确认核算单"""
        school_id = await get_school_id()
        
        report = await self.model.filter(id=report_id, school_id=school_id).first()
        if not report:
            raise ValueError("核算单不存在或不属于当前学校")
        
        if report.status == "confirmed":
            raise ValueError("核算单已确认，无法重复确认")
        
        report.status = "confirmed"
        await report.save()
        
        return report

    @atomic()
    async def delete_report(self, report_id: int):
        """删除核算单"""
        school_id = await get_school_id()
        
        report = await self.model.filter(id=report_id, school_id=school_id).first()
        if not report:
            raise ValueError("核算单不存在或不属于当前学校")
        
        if report.status == "confirmed":
            raise ValueError("已确认的核算单无法删除")
        
        await report.delete()


daily_cost_report_controller = DailyCostReportController()
