from datetime import datetime
import uuid
from tortoise.transactions import atomic
from tortoise.expressions import Q
from typing import List, Dict, Any


from app.core.crud import CRUDBase
from app.models.orders import Order, OrderStatus, OrderType
from app.models.food_stuff import FoodStuff, FoodStuffStore
from app.schemas.orders import OrderCreate
from app.controllers.utils import get_school_id, get_supplier_id
from app.controllers.food_stuff_store import food_stuff_store_controller


class OrderController(CRUDBase[Order, OrderCreate, OrderCreate]):
    def __init__(self):
        super().__init__(model=Order)

    async def _process_inventory_and_order(self, order_items: List[Dict[str, Any]], school_id: int):
        """
        处理库存检查和扣除，返回需要下单的食材列表
        """
        processed_items = []
        inventory_changes = []
        
        # 收集所有需要处理的食材ID
        food_stuff_ids = [item.get('id') for item in order_items if item.get('id')]
        
        # 获取所有相关食材的初始总库存
        initial_total_stores = {}
        if food_stuff_ids:
            # 获取所有相关食材的库存记录
            all_stores = await FoodStuffStore.filter(
                food_stuff_id__in=food_stuff_ids,
                school_id=school_id
            )
            
            # 计算每个食材的总库存
            for store in all_stores:
                if store.food_stuff_id not in initial_total_stores:
                    initial_total_stores[store.food_stuff_id] = 0
                initial_total_stores[store.food_stuff_id] += store.store_count
        
        for item in order_items:
            food_stuff_id = item.get('id')
            required_quantity = float(item.get('quantity', 0))
            
            if not food_stuff_id or required_quantity <= 0:
                continue
            
            # 获取食材信息以获取supplier_id
            food_stuff = await FoodStuff.get_or_none(id=food_stuff_id)
            if not food_stuff:
                continue
            
            # 记录该食材的初始总库存
            initial_store = initial_total_stores.get(food_stuff_id, 0)
            
            # 按照先进先出原则扣除库存
            deduction_result = await self._deduct_inventory_fifo(
                food_stuff_id=food_stuff_id,
                school_id=school_id,
                supplier_id=food_stuff.supplier_id,
                required_quantity=required_quantity
            )

            # 添加合并后的库存变化记录
            if deduction_result['inventory_changes']:
                # 计算总变化量
                total_change = sum(change['change_count'] for change in deduction_result['inventory_changes'])
                
                # 添加一条合并后的记录
                inventory_changes.append({
                    "food_stuff_id": food_stuff_id,
                    "before_count": initial_store,
                    "change_count": total_change
                })

            # 如果需要下单
            if deduction_result['remaining_quantity'] > 0:
                processed_items.append({
                    **item,
                    'quantity': deduction_result['remaining_quantity'],
                    'immediate_consume': True,  # 添加立即消耗标记
                    'consume_quantity': required_quantity,  # 记录总消耗量
                    'recipe_id': item.get('recipe_id')  # 传递recipe_id
                })
        
        # 记录库存变化
        if inventory_changes:
            await food_stuff_store_controller.record_store_change(
                operation_type="下单库存消耗",
                remark="根据食谱需求自动扣除库存",
                food_stuff_changes=inventory_changes
            )
            
        return processed_items

    async def _deduct_inventory_fifo(self, food_stuff_id: int, school_id: int, supplier_id: int, required_quantity: float):
        """
        按照先进先出原则扣除库存

        Args:
            food_stuff_id: 食材ID
            school_id: 学校ID
            supplier_id: 供应商ID
            required_quantity: 需要扣除的数量

        Returns:
            dict: {
                'remaining_quantity': 剩余需要下单的数量,
                'inventory_changes': 库存变化记录列表
            }
        """
        # 获取所有相同食材的库存记录，按创建时间排序（先进先出）
        stores = await FoodStuffStore.filter(
            food_stuff_id=food_stuff_id,
            school_id=school_id,
            supplier_id=supplier_id
        ).order_by('created_at')

        remaining_quantity = required_quantity
        inventory_changes = []

        for store in stores:
            if remaining_quantity <= 0:
                break

            before_count = store.store_count

            if store.store_count >= remaining_quantity:
                # 当前记录库存充足，扣除所需数量
                store.store_count -= remaining_quantity

                # 记录库存变化
                inventory_changes.append({
                    "food_stuff_id": food_stuff_id,
                    "before_count": before_count,
                    "change_count": -remaining_quantity
                })

                # 如果库存扣除后为0，删除该记录
                if store.store_count <= 0:
                    await store.delete()
                else:
                    await store.save()

                remaining_quantity = 0

            else:
                # 当前记录库存不足，全部扣除
                deducted_amount = store.store_count

                # 记录库存变化
                inventory_changes.append({
                    "food_stuff_id": food_stuff_id,
                    "before_count": before_count,
                    "change_count": -deducted_amount
                })

                remaining_quantity -= deducted_amount

                # 删除该记录（库存为0）
                await store.delete()

        return {
            'remaining_quantity': remaining_quantity,
            'inventory_changes': inventory_changes
        }

    async def _group_order_items(self, order_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将订单项按照供应商和商品类型分组"""
        # 获取食材信息并按供应商分组
        grouped_items = {}
        
        for item in order_items:
            food_stuff_id = item.get('id')
            if not food_stuff_id:
                continue
                
            # 根据food_stuff_id查询食材信息获取supplier_id
            try:
                food_stuff = await FoodStuff.get(id=food_stuff_id)
                supplier_id = food_stuff.supplier_id
            except Exception:
                # 如果查询失败，使用默认供应商ID
                supplier_id = 1
            
            if supplier_id not in grouped_items:
                grouped_items[supplier_id] = []
                
            grouped_items[supplier_id].append(item)
        
        # 将分组后的商品转换为订单列表
        orders = []
        for supplier_id, items in grouped_items.items():
            if items:
                orders.append({
                    'supplier_id': supplier_id,
                    'order_items': items,
                    'order_type': OrderType.LOOSE_FOOD  # 默认为散货食材
                })
        
        return orders

    @atomic()
    async def create_order(self, obj_in: OrderCreate):
        school_id = await get_school_id()
        
        # 处理库存检查和扣除，获取需要下单的食材
        items_to_order = await self._process_inventory_and_order(obj_in.order_items, school_id)
        
        # 如果没有需要下单的食材，直接返回
        if not items_to_order:
            return {"message": "所有食材库存充足，无需下单"}
        
        # 对需要下单的食材进行分组
        grouped_orders = await self._group_order_items(items_to_order)

        # 为每个分组创建订单
        created_orders = []
        for order_data in grouped_orders:
            # 生成订单编号
            order_number = f"{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:4]}"

            # 创建订单
            order = await self.create(obj_in={
                "order_type": order_data['order_type'],
                "order_status": OrderStatus.PENDING,
                "order_number": order_number,
                "supplier_id": order_data['supplier_id'],
                "order_items": order_data['order_items'],
                "order_remark": obj_in.order_remark or "",
                "school_id": school_id
            })
            # 转换为字典格式，确保可以JSON序列化
            order_dict = {
                "id": order.id,
                "order_type": order.order_type,
                "order_status": order.order_status,
                "order_number": order.order_number,
                "supplier_id": order.supplier_id,
                "school_id": order.school_id,
                "order_remark": order.order_remark,
                "order_items": order.order_items,
                "order_total": order.order_total,
                "created_at": order.created_at.isoformat() if order.created_at else None,
                "updated_at": order.updated_at.isoformat() if order.updated_at else None
            }
            created_orders.append(order_dict)
            
        return {"message": f"成功创建{len(created_orders)}个订单", "orders": created_orders}

    @atomic()
    async def create_direct_order(self, obj_in: OrderCreate):
        """
        直接下单，不检查库存，不扣除库存
        用于食材管理页面的购物车下单
        """
        school_id = await get_school_id()
        
        # 对订单项按供应商分组
        grouped_orders = self._group_order_items_by_supplier(obj_in.order_items)

        # 为每个分组创建订单
        created_orders = []
        for order_data in grouped_orders:
            # 生成订单编号
            order_number = f"{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:4]}"

            # 创建订单
            order = await self.create(obj_in={
                "order_type": order_data['order_type'],
                "order_status": OrderStatus.PENDING,
                "order_number": order_number,
                "supplier_id": order_data['supplier_id'],
                "order_items": order_data['order_items'],
                "order_remark": obj_in.order_remark or "食材管理直接下单",
                "school_id": school_id
            })
            # 转换为字典格式，确保可以JSON序列化
            order_dict = {
                "id": order.id,
                "order_type": order.order_type,
                "order_status": order.order_status,
                "order_number": order.order_number,
                "supplier_id": order.supplier_id,
                "school_id": order.school_id,
                "order_remark": order.order_remark,
                "order_items": order.order_items,
                "order_total": order.order_total,
                "created_at": order.created_at.isoformat() if order.created_at else None,
                "updated_at": order.updated_at.isoformat() if order.updated_at else None
            }
            created_orders.append(order_dict)
            
        return {"message": f"成功创建{len(created_orders)}个订单", "orders": created_orders}

    def _group_order_items_by_supplier(self, order_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        将订单项按照供应商分组（用于直接下单）
        """
        grouped_items = {}
        
        for item in order_items:
            supplier_id = item.get('supplierId', 1)  # 从前端传递的供应商ID
            
            if supplier_id not in grouped_items:
                grouped_items[supplier_id] = []
                
            grouped_items[supplier_id].append(item)
        
        # 将分组后的商品转换为订单列表
        orders = []
        for supplier_id, items in grouped_items.items():
            if items:
                # 根据食材类型确定订单类型
                order_type = OrderType.BULK_COMMODITY if items[0].get('type') == 1 else OrderType.LOOSE_FOOD
                orders.append({
                    'supplier_id': supplier_id,
                    'order_items': items,
                    'order_type': order_type
                })
        
        return orders

    async def get_order_list(self, *, page: int = 1, page_size: int = 10, search: Q = None):
        if search is None:
            search = Q()

        total = await self.model.filter(search).count()
        items = await self.model.filter(search).order_by("-created_at").offset((page - 1) * page_size).limit(page_size)
        
        # 获取所有供应商信息
        from app.models.admin import Dept
        supplier_ids = [item.supplier_id for item in items]
        suppliers = await Dept.filter(id__in=supplier_ids).all()
        supplier_dict = {supplier.id: supplier.name for supplier in suppliers}
        
        # 为每个订单添加供应商名称
        enriched_items = []
        for item in items:
            item_dict = await item.to_dict()
            item_dict['supplier_name'] = supplier_dict.get(item.supplier_id, '未知供应商')
            enriched_items.append(item_dict)
        
        return total, enriched_items

    @atomic()
    async def shipment(self, order_id: int, order_items: List[Dict[str, Any]]):
        print(order_id, order_items)
        order = await self.get(id=order_id)
        if not order:
            raise ValueError("订单不存在")
        if order.order_status != OrderStatus.PENDING:
            raise ValueError("订单状态不是待发货")

        supplier_id = await get_supplier_id()
        if order.supplier_id != supplier_id:
            raise ValueError("订单供应商不匹配")
        items = order.order_items
        for item in items:
            for order_item in order_items:
                if item.get('id') == order_item.get("id"):
                    item['price'] = order_item.get("price")
                    # 添加生产日期和保质期信息
                    if order_item.get("production_date"):
                        item['production_date'] = order_item.get("production_date")
                    if order_item.get("shelf_life"):
                        item['shelf_life'] = order_item.get("shelf_life")
        order.order_status = OrderStatus.SHIPPED
        order.order_items = items
        order.order_shipped_date = datetime.now()
        await order.save()

    async def get_order_detail(self, order_id: str, token: str = None):
        """
        获取订单食材列表

        Args:
            order_id: 订单ID或订单编号
            token: 验证token，如果提供则不需要登录验证

        Returns:
            list: 订单食材列表
        """
        # 查询订单 - 支持通过ID或订单编号查询
        try:
            # 尝试将order_id转换为整数（如果是数字ID）
            id_value = int(order_id)
            order = await self.get(id=id_value)
        except ValueError:
            # 如果不是数字，则按订单编号查询
            order = await self.model.filter(order_number=order_id).first()

        if not order:
            raise ValueError("订单不存在")

        # 如果提供了token，验证token
        if token:
            # 查询学校
            from app.models.admin import Dept
            school = await Dept.filter(id=order.school_id).first()
            if not school or school.token != token:
                raise ValueError("无效的token")

        # 只返回订单食材列表
        return order.order_items


order_controller = OrderController()
