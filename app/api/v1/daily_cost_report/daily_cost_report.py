from fastapi import APIRouter, Query, Body, Depends
from datetime import datetime, date

from app.controllers.daily_cost_report import daily_cost_report_controller
from app.schemas import Success, SuccessExtra, Fail
from app.schemas.daily_cost_report import DailyCostReportGenerate, DailyCostReportSearch

router = APIRouter()


@router.get("/list", summary="查询日成本核算单列表")
async def list_daily_cost_report(
    search: DailyCostReportSearch = Depends(),
):
    """获取日成本核算单列表"""
    try:
        result = await daily_cost_report_controller.get_report_list(search)
        return SuccessExtra(
            data=result["data"],
            total=result["total"],
            page=search.current,
            page_size=search.size
        )
    except Exception as e:
        return Fail(msg=f"获取核算单列表失败: {str(e)}")


@router.get("/get", summary="获取日成本核算单详情")
async def get_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """获取日成本核算单详情"""
    try:
        report = await daily_cost_report_controller.get_report_detail(id)
        return Success(data=report)
    except Exception as e:
        return Fail(msg=f"获取核算单详情失败: {str(e)}")


@router.post("/generate", summary="生成日成本核算单")
async def generate_daily_cost_report(
    generate_data: DailyCostReportGenerate = Body(...),
):
    """生成日成本核算单"""
    try:
        report = await daily_cost_report_controller.generate_report(generate_data)
        return Success(data={"id": report.id}, msg="生成核算单成功")
    except Exception as e:
        return Fail(msg=f"生成核算单失败: {str(e)}")


@router.post("/confirm", summary="确认日成本核算单")
async def confirm_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """确认日成本核算单"""
    try:
        await daily_cost_report_controller.confirm_report(id)
        return Success(msg="确认核算单成功")
    except Exception as e:
        return Fail(msg=f"确认核算单失败: {str(e)}")


@router.delete("/delete", summary="删除日成本核算单")
async def delete_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """删除日成本核算单"""
    try:
        await daily_cost_report_controller.delete_report(id)
        return Success(msg="删除核算单成功")
    except Exception as e:
        return Fail(msg=f"删除核算单失败: {str(e)}")


@router.get("/check-date", summary="检查指定日期是否可以生成核算单")
async def check_date_available(
    report_date: str = Query(..., description="核算日期", example="2025-03-11"),
):
    """检查指定日期是否可以生成核算单"""
    try:
        # 解析日期
        parsed_date = datetime.strptime(report_date, "%Y-%m-%d").date()
        
        # 检查是否已存在核算单
        from app.controllers.utils import get_school_id
        school_id = await get_school_id()
        
        existing_report = await daily_cost_report_controller.model.filter(
            report_date=parsed_date,
            school_id=school_id
        ).first()
        
        if existing_report:
            return Success(data={
                "available": False,
                "message": f"日期 {report_date} 的核算单已存在",
                "existing_report_id": existing_report.id
            })
        
        # 检查是否有食谱
        from app.models.food_menu import Recipe
        recipe = await Recipe.filter(
            recipe_date=parsed_date,
            school_id=school_id,
            is_active=True
        ).first()
        
        if not recipe:
            return Success(data={
                "available": False,
                "message": f"日期 {report_date} 没有可用的食谱"
            })
        
        return Success(data={
            "available": True,
            "message": f"日期 {report_date} 可以生成核算单",
            "recipe_id": recipe.id,
            "recipe_name": recipe.recipe_name
        })
        
    except ValueError:
        return Fail(msg="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        return Fail(msg=f"检查日期失败: {str(e)}")


@router.get("/export", summary="导出日成本核算单")
async def export_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """导出日成本核算单为Excel格式"""
    try:
        # 获取核算单详情
        report = await daily_cost_report_controller.get_report_detail(id)
        
        # 这里可以实现Excel导出逻辑
        # 暂时返回数据，前端可以用于生成表格
        return Success(data=report, msg="获取导出数据成功")
        
    except Exception as e:
        return Fail(msg=f"导出核算单失败: {str(e)}")
