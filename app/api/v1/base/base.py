from datetime import datetime, timedelta, timezone
import hashlib

from fastapi import APIRouter, Body, Query, Form
from pydantic import BaseModel, Field

from app.controllers.user import user_controller
from app.controllers.food_stuff_store import food_stuff_store_controller
from app.controllers.dept import dept_controller
from app.controllers.order import order_controller
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.models.admin import Api, Menu, Role, User
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.login import *
from app.schemas.users import UpdatePassword
from app.settings import settings
from app.utils.jwt import create_access_token
from app.utils.password import get_password_hash, verify_password

router = APIRouter()


@router.post("/access_token", summary="获取token")
async def login_access_token(credentials: CredentialsSchema):
    user: User = await user_controller.authenticate(credentials)
    await user_controller.update_last_login(user.id)
    access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    expire = datetime.now(timezone.utc) + access_token_expires

    data = JWTOut(
        access_token=create_access_token(
            data=JWTPayload(
                user_id=user.id,
                username=user.username,
                is_superuser=user.is_superuser,
                exp=expire,
            )
        ),
        username=user.username,
    )
    return Success(data=data.model_dump())


@router.get("/userinfo", summary="查看用户信息", dependencies=[DependAuth])
async def get_userinfo():
    user_id = CTX_USER_ID.get()
    user_obj = await user_controller.get(id=user_id)
    data = await user_obj.to_dict(m2m=True, exclude_fields=["password"])
    data["avatar"] = "https://avatars.githubusercontent.com/u/54677442?v=4"
    return Success(data=data)


@router.get("/usermenu", summary="查看用户菜单", dependencies=[DependAuth])
async def get_user_menu():
    user_id = CTX_USER_ID.get()
    user_obj = await User.filter(id=user_id).first()
    menus: list[Menu] = []
    if user_obj.is_superuser:
        menus = await Menu.all()
    else:
        role_objs: list[Role] = await user_obj.roles
        for role_obj in role_objs:
            menu = await role_obj.menus
            menus.extend(menu)
        menus = list(set(menus))
    parent_menus: list[Menu] = []
    for menu in menus:
        if menu.parent_id == 0:
            parent_menus.append(menu)
    res = []
    for parent_menu in parent_menus:
        parent_menu_dict = await parent_menu.to_dict()
        parent_menu_dict["children"] = []
        for menu in menus:
            if menu.parent_id == parent_menu.id:
                parent_menu_dict["children"].append(await menu.to_dict())
        res.append(parent_menu_dict)
    return Success(data=res)


@router.get("/userapi", summary="查看用户API", dependencies=[DependAuth])
async def get_user_api():
    user_id = CTX_USER_ID.get()
    user_obj = await User.filter(id=user_id).first()
    if user_obj.is_superuser:
        api_objs: list[Api] = await Api.all()
        apis = [api.method.lower() + api.path for api in api_objs]
        return Success(data=apis)
    role_objs: list[Role] = await user_obj.roles
    apis = []
    for role_obj in role_objs:
        api_objs: list[Api] = await role_obj.apis
        apis.extend([api.method.lower() + api.path for api in api_objs])
    apis = list(set(apis))
    return Success(data=apis)


@router.post("/update_password", summary="修改密码", dependencies=[DependAuth])
async def update_user_password(req_in: UpdatePassword):
    user_id = CTX_USER_ID.get()
    user = await user_controller.get(user_id)
    verified = verify_password(req_in.old_password, user.password)
    if not verified:
        return Fail(msg="旧密码验证错误！")
    user.password = get_password_hash(req_in.new_password)
    await user.save()
    return Success(msg="修改成功")


@router.post("/inbound", summary="确认入库")
async def inbound(
    order_id: str = Form(..., description="订单ID或订单编号"),
    token: str = Form(..., description="验证token")
):
    """
    确认入库，通过token验证，不需要登录（支持form-data格式）
    """
    try:
        await food_stuff_store_controller.confirm_receive(order_id=order_id, token=token)
        return Success(msg="入库成功")
    except Exception as e:
        return Fail(msg=str(e))

class UpdateStudentCount(BaseModel):
    token: str = Field(..., description="token", example="123456")
    student_count: int = Field(..., description="学生数量", example=30)

class UpdateStudentCount(BaseModel):
    token: str = Field(..., description="token", example="123456")
    student_count: int = Field(..., description="学生数量", example=30)

@router.post("/student-count/update", summary="更新学生数量")
async def update_student_count(
    data: UpdateStudentCount,
):
    try:
        await dept_controller.update_student_count(token=data.token, student_count=data.student_count)
        return Success(msg="更新成功")
    except Exception as e:
        return Fail(msg=str(e))

@router.post("/class/login", summary="班级登录")
async def class_login(
    sign: str = Body(..., description="签名"),
    phone: str = Body(..., description="班主任手机号"),
    t: str = Body(..., description="时间戳"),
):
    #(phone + t + secret_key + "sec")md5
    secret_key = "sleuFDUOAJFDSjfkdui132ds"
    calculate_sign = hashlib.md5((phone + t + secret_key + "sec").encode()).hexdigest()
    if calculate_sign != sign:
        return Fail(msg="签名错误")
    try:
        class_obj = await dept_controller.class_login(phone=phone)
        return SuccessExtra(data=class_obj.to_dict(), msg="登录成功")
    except Exception as e:
        return Fail(msg=str(e))


class OrderDetailRequest(BaseModel):
    order_id: int = Field(..., description="订单ID", example=1)
    token: str = Field(..., description="验证token", example="abc123")


@router.post("/order/detail", summary="查看订单食材列表")
async def get_order_detail_post(
    order_id: str = Form(..., description="订单ID"),
    token: str = Form(..., description="验证token"),
):
    """
    查看订单食材列表，通过token验证，不需要登录（POST方法，支持form-data格式）
    """
    try:
        order_items = await order_controller.get_order_detail(order_id=order_id, token=token)
        return Success(data=order_items)
    except Exception as e:
        return Fail(msg=str(e))


@router.get("/order/detail", summary="查看订单食材列表")
async def get_order_detail_get(
    order_id: str = Query(..., description="订单ID或订单编号"),
    token: str = Query(..., description="验证token"),
):
    """
    查看订单食材列表，通过token验证，不需要登录（GET方法）
    """
    try:
        order_items = await order_controller.get_order_detail(order_id=order_id, token=token)
        return Success(data=order_items)
    except Exception as e:
        return Fail(msg=str(e))