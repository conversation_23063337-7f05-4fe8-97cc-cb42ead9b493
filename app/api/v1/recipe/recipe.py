from fastapi import APIRouter, Query, Body
from datetime import datetime

from app.controllers.recipe import recipe_controller
from app.schemas import Success, SuccessExtra
from app.schemas.recipe import RecipeCreate, RecipeUpdate, RecipeSearch

router = APIRouter()


@router.get("/list", summary="查询食谱列表")
async def list_recipe(
    recipe_date: str = Query(None, description="食谱日期"),
    page: int = Query(1, description="当前页码"),
    page_size: int = Query(10, description="每页数量"),
):
    # 处理日期参数
    parsed_recipe_date = None
    if recipe_date:
        try:
            parsed_recipe_date = datetime.strptime(recipe_date, "%Y-%m-%d").date()
        except ValueError:
            parsed_recipe_date = None

    search = RecipeSearch(
        recipeDate=parsed_recipe_date,
        current=page,
        size=page_size
    )
    result = await recipe_controller.get_recipe_list(search)
    return SuccessExtra(
        data=result["data"],
        total=result["total"],
        page=page,
        page_size=page_size
    )


@router.get("/get", summary="获取食谱详情")
async def get_recipe(
    id: int = Query(..., description="食谱ID"),
):
    recipe = await recipe_controller.get_recipe_detail(id)
    return Success(data=recipe)


@router.post("/create", summary="创建食谱")
async def create_recipe(
    recipe_in: RecipeCreate,
):
    await recipe_controller.create_recipe(obj_in=recipe_in)
    return Success(msg="创建成功")


@router.post("/update", summary="更新食谱")
async def update_recipe(
    id: int = Query(..., description="食谱ID"),
    recipe_in: RecipeUpdate = Body(...),
):
    await recipe_controller.update_recipe(recipe_id=id, obj_in=recipe_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除食谱")
async def delete_recipe(
    id: int = Query(..., description="食谱ID"),
):
    await recipe_controller.delete_recipe(recipe_id=id)
    return Success(msg="删除成功")


@router.get("/by-date", summary="根据日期获取食谱信息")
async def get_recipe_by_date(
    recipe_date: str = Query(..., description="食谱日期"),
):
    """用于下单功能获取指定日期的食谱信息"""
    try:
        parsed_date = datetime.strptime(recipe_date, "%Y-%m-%d").date()
    except ValueError:
        return Success(data=None, msg="日期格式错误")
    
    try:
        recipe_data = await recipe_controller.get_recipe_by_date_for_order(parsed_date)
        return Success(data=recipe_data)
    except ValueError as e:
        return Success(data=None, msg=str(e))
