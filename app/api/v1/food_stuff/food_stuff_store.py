from fastapi import APIRouter, Query, HTTPException, Body, Depends
from typing import Optional
from tortoise.expressions import Q

from app.controllers.food_stuff_store import food_stuff_store_controller
from app.schemas.base import Success, SuccessExtra
from app.schemas.food_stuff_store import FoodStuffStoreCreate, FoodStuffStoreUpdate, FoodStuffStoreSearch, FoodStuffStoreRecordSearch
from pydantic import BaseModel
from typing import Any

router = APIRouter()


@router.get("/list", summary="获取食材库存列表")
async def list_food_stuff_store(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    food_stuff_name: str = Query("", description="食材名称", alias="foodStuffName"),
    food_stuff_ids: str = Query(None, description="食材ID列表，多个用逗号分隔", alias="foodStuffIds"),
    school_id: Optional[int] = Query(None, description="学校ID", alias="schoolId"),
    min_price: Optional[float] = Query(None, description="最低价格", alias="minPrice"),
    max_price: Optional[float] = Query(None, description="最高价格", alias="maxPrice"),
):
    # 构建查询条件
    q = Q()
    if food_stuff_name:
        # 通过食材名称查询，需要先查询食材表
        from app.models.food_stuff import FoodStuff
        food_stuffs = await FoodStuff.filter(food_stuff_name__contains=food_stuff_name)
        if food_stuffs:
            food_stuff_ids_list = [fs.id for fs in food_stuffs]
            q &= Q(food_stuff_id__in=food_stuff_ids_list)
        else:
            # 如果没有找到匹配的食材，返回空结果
            return SuccessExtra(data=[], total=0, page=page, page_size=page_size)

    if food_stuff_ids:
        food_stuff_ids_list = food_stuff_ids.split(",")
        q &= Q(food_stuff_id__in=food_stuff_ids_list)

    if school_id:
        q &= Q(school_id=school_id)

    # 添加价格范围查询
    if min_price is not None:
        q &= Q(price__gte=min_price)

    if max_price is not None:
        q &= Q(price__lte=max_price)
    
    # 查询数据
    total, items = await food_stuff_store_controller.list(page=page, page_size=page_size, search=q)
    
    # 处理数据，添加关联信息
    data = []
    for item in items:
        item_dict = await item.to_dict()
        item_dict['unitName'] = getattr(item, 'unit_name', '')
        item_dict['foodStuffName'] = getattr(item, 'food_stuff_name', '')
        item_dict['supplierName'] = getattr(item, 'supplier_name', '')
        data.append(item_dict)
    
    # 返回结果
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/list-old", summary="获取食材库存列表（旧接口）")
async def list_food_stuff_store_old(
    current: int = Query(1, description="当前页"),
    size: int = Query(10, description="每页数量"),
    food_stuff_name: str = Query(None, description="食材名称", alias="foodStuffName"),
    food_stuff_ids: str = Query(None, description="食材ID列表，多个用逗号分隔", alias="foodStuffIds"),    
    school_id: int = Query(None, description="学校ID", alias="schoolId"),
):
    """保持旧接口兼容性"""
    search = {
        "current": current,
        "size": size,
        "food_stuff_name": food_stuff_name,
        "food_stuff_ids": food_stuff_ids,
        "school_id": school_id
    }
    data = await food_stuff_store_controller.get_list(search)
    return Success(data=data)


@router.get("/get", summary="获取食材库存详情")
async def get_food_stuff_store(
    id: int = Query(..., description="库存ID"),
):
    store_obj = await food_stuff_store_controller.get(id=id)
    data = await store_obj.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建食材库存")
async def create_food_stuff_store(
    store_in: FoodStuffStoreCreate,
):
    await food_stuff_store_controller.create_store(obj_in=store_in)
    return Success(msg="Created Successfully")


@router.post("/update", summary="更新食材库存")
async def update_food_stuff_store(
    store_in: FoodStuffStoreUpdate,
):
    await food_stuff_store_controller.update_store(obj_in=store_in)
    return Success(msg="Updated Successfully")


@router.delete("/delete", summary="删除食材库存")
async def delete_food_stuff_store(
    id: int = Query(..., description="库存ID"),
):
    await food_stuff_store_controller.remove(id=id)
    return Success(msg="Deleted Successfully")


# class ConfirmReceive(BaseModel):
#     order_id: int
#     token: str

# @router.post("/confirm-receive", summary="确认入库")
# async def confirm_receive(
#     receive: ConfirmReceive = Body(..., description="入库信息"),
# ):
#     try:
#         await food_stuff_store_controller.confirm_receive(order_id=receive.order_id, token=receive.token)
#         return Success(msg="入库成功")
#     except Exception as e:
#         return Fail(msg=str(e))

@router.get("/records", summary="获取食材库存变化记录列表")
async def list_food_stuff_store_records(
    search: FoodStuffStoreRecordSearch = Depends(),
):
    """获取食材库存变化记录列表"""
    total, items = await food_stuff_store_controller.get_store_records(search)
    # 处理数据，添加关联信息
    data = []
    for item in items:
        item_dict = await item.to_dict()
        # 可以在这里添加额外的字段处理，类似 food_stuff.py 中的 unit_name
        data.append(item_dict)
    
    # 返回结果
    return SuccessExtra(data=data, total=total, page=search.current, page_size=search.size)


@router.get("/recipe-store", summary="获取食谱所需食材的库存总量")
async def get_recipe_store(
    food_stuff_ids: str = Query(..., description="食材ID列表，多个用逗号分隔"),
):
    """获取食谱所需食材的库存总量，将相同食材ID的所有库存记录累加"""
    try:
        ids_list = [int(id_str) for id_str in food_stuff_ids.split(',') if id_str.strip()]
        stores = await food_stuff_store_controller.get_food_stuff_store_for_recipe(ids_list)
        return Success(data=stores)
    except Exception as e:
        return Fail(msg=f"获取库存失败: {str(e)}")
