from fastapi import APIRouter

from app.core.dependency import Depend<PERSON>er<PERSON>son

from .apis import apis_router
from .auditlog import auditlog_router
from .base import base_router
from .depts import depts_router
from .menus import menus_router
from .roles import roles_router
from .users import users_router
from .orders import orders_router
from .dish import dish_router
from .recipe import recipe_router
# 导入新增的API路由
from app.api.v1.food_stuff import food_stuff, food_stuff_store, unit, nutrient
from app.api.v1.daily_cost_report import daily_cost_report

v1_router = APIRouter()

v1_router.include_router(base_router, prefix="/base")
v1_router.include_router(users_router, prefix="/user", dependencies=[DependPermisson])
v1_router.include_router(roles_router, prefix="/role", dependencies=[DependPermisson])
v1_router.include_router(menus_router, prefix="/menu", dependencies=[DependPermisson])
v1_router.include_router(apis_router, prefix="/api", dependencies=[DependPermisson])
v1_router.include_router(depts_router, prefix="/dept", dependencies=[DependPermisson])
v1_router.include_router(auditlog_router, prefix="/auditlog", dependencies=[DependPermisson])
v1_router.include_router(orders_router, prefix="/orders", dependencies=[DependPermisson])
v1_router.include_router(recipe_router, prefix="/food-menu", dependencies=[DependPermisson])
v1_router.include_router(dish_router, prefix="/dish", tags=["菜品管理"], dependencies=[DependPermisson])
# 注册食材相关路由
v1_router.include_router(food_stuff.router, prefix="/food-stuff", tags=["食材管理"], dependencies=[DependPermisson])
v1_router.include_router(unit.router, prefix="/unit", tags=["单位管理"], dependencies=[DependPermisson])
v1_router.include_router(nutrient.router, prefix="/nutrient", tags=["营养素管理"], dependencies=[DependPermisson])
v1_router.include_router(food_stuff_store.router, prefix="/food-stuff-store", tags=["食材库存管理"], dependencies=[DependPermisson])
v1_router.include_router(daily_cost_report.router, prefix="/daily-cost-report", tags=["日成本核算"], dependencies=[DependPermisson])
