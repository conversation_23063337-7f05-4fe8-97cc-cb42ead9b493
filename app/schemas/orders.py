from typing import Annotated, List, Dict, Any

from pydantic import BaseModel, Field



class OrderBase(BaseModel):
    order_type: Annotated[int | None, Field(alias="orderType", title="订单类型")] = None
    order_status: Annotated[int | None, Field(alias="orderStatus", title="订单状态")] = None
    order_number: Annotated[str | None, Field(alias="orderNumber", title="订单编号")] = None
    order_paid_date: Annotated[str | None, Field(alias="orderPaidDate", title="订单支付时间")] = None
    order_confirm_date: Annotated[str | None, Field(alias="orderConfirmDate", title="订单确认入库时间")] = None
    supplier_id: Annotated[int | None, Field(alias="bySupplierId", title="订单供应商ID")] = None
    school_id: Annotated[int | None, Field(alias="bySchoolId", title="订单来源学校ID")] = None
    order_remark: Annotated[str | None, Field(alias="orderRemark", title="订单备注")] = None
    order_items: Annotated[List[Dict[str, Any]] | None, Field(alias="orderItems", title="订单商品")] = None
    order_total: Annotated[float | None, Field(alias="orderTotal", title="订单总金额")] = None
    recipe_id: Annotated[int | None, Field(alias="recipeId", title="食谱ID")] = None

    class Config:
        allow_extra = True
        populate_by_name = True


class OrderCreate(OrderBase):
    # order_type: Annotated[int, Field(alias="orderType", title="订单类型")]
    # order_number: Annotated[str, Field(alias="orderNumber", title="订单编号")]
    # supplier_id: Annotated[int, Field(alias="bySupplierId", title="订单供应商ID")]
    # school_id: Annotated[int, Field(alias="bySchoolId", title="订单来源学校ID")]
    order_items: Annotated[List[Dict[str, Any]], Field(alias="orderItems", title="订单商品")]


class OrderUpdate(OrderBase):
    ...


class OrderResponse(OrderBase):
    id: Annotated[int, Field(title="订单ID")]

    class Config:
        from_attributes = True


class OrderStatusUpdate(BaseModel):
    order_status: Annotated[int, Field(alias="orderStatus", title="订单状态")]

    class Config:
        populate_by_name = True 