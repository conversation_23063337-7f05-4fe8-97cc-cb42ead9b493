from datetime import date
from typing import Annotated, Optional

from pydantic import BaseModel, Field


class UnitBase(BaseModel):
    unit_name: Annotated[str | None, Field(alias="unitName", title="单位名称")] = None

    class Config:
        populate_by_name = True


class UnitCreate(UnitBase):
    unit_name: Annotated[str, Field(alias="unitName", title="单位名称")]


class UnitUpdate(UnitBase):
    ...


class NutrientBase(BaseModel):
    nutrient_name: Annotated[str | None, Field(alias="nutrientName", title="营养素名称")] = None

    class Config:
        populate_by_name = True


class NutrientCreate(NutrientBase):
    nutrient_name: Annotated[str, Field(alias="nutrientName", title="营养素名称")]


class NutrientUpdate(NutrientBase):
    ...


class FoodStuffBase(BaseModel):
    food_stuff_name: Annotated[str | None, Field(alias="foodStuffName", title="食材名称")] = None
    food_stuff_type: Annotated[int | None, Field(alias="foodStuffType", title="食材类型")] = None
    food_stuff_unit_id: Annotated[int | None, Field(alias="foodStuffUnitId", title="计量单位ID")] = None
    nutrient_info: Annotated[list | None, Field(alias="nutrientInfo", title="营养信息")] = None
    # 移除价格、生产日期、保质期字段，这些信息现在存储在订单明细中
    # food_stuff_price: Annotated[float | None, Field(alias="foodStuffPrice", title="食材价格")] = None
    # production_date: Annotated[date | None, Field(alias="productionDate", title="生产日期")] = None
    # shelf_life: Annotated[int | None, Field(alias="shelfLife", title="保质期(天)")] = None

    class Config:
        populate_by_name = True


class FoodStuffCreate(FoodStuffBase):
    food_stuff_name: Annotated[str, Field(alias="foodStuffName", title="食材名称")]
    food_stuff_type: Annotated[int, Field(alias="foodStuffType", title="食材类型")]
    food_stuff_unit_id: Annotated[int, Field(alias="foodStuffUnitId", title="计量单位ID")]
    # 移除价格字段，现在存储在订单明细中
    # food_stuff_price: Annotated[float | None, Field(alias="foodStuffPrice", title="食材价格")] = None
    supplier_id: Annotated[int | None, Field(alias="supplierId", title="供应商ID")] = None


class FoodStuffUpdate(FoodStuffBase):
    id: Annotated[int, Field(alias="id", title="食材ID")]
    ...


class FoodStuffSearch(FoodStuffBase):
    current: Annotated[int | None, Field(description="页码")] = 1
    size: Annotated[int | None, Field(description="每页数量")] = 10


__all__ = [
    "UnitBase", "UnitCreate", "UnitUpdate",
    "NutrientBase", "NutrientCreate", "NutrientUpdate",
    "FoodStuffBase", "FoodStuffCreate", "FoodStuffUpdate", "FoodStuffSearch"
]