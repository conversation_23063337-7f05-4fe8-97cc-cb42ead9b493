from typing import Annotated, List, Optional
from datetime import date

from pydantic import BaseModel, Field


class RecipeMealBase(BaseModel):
    """餐次基础模型"""
    meal_type: Annotated[int, Field(description="餐次类型")]
    dish_list: Annotated[List[dict], Field(description="菜品列表")]
    is_active: Annotated[bool, Field(description="是否启用")] = True

    class Config:
        populate_by_name = True


class RecipeMealCreate(RecipeMealBase):
    """创建餐次模型"""
    pass


class RecipeMealUpdate(RecipeMealBase):
    """更新餐次模型"""
    id: Annotated[int, Field(description="餐次ID")]
    meal_type: Annotated[Optional[int], Field(description="餐次类型")] = None
    dish_list: Annotated[Optional[List[dict]], Field(description="菜品列表")] = None
    is_active: Annotated[Optional[bool], Field(description="是否启用")] = None


class RecipeBase(BaseModel):
    """食谱基础模型"""
    recipe_date: Annotated[date, Field(alias="recipeDate", description="食谱日期")]
    recipe_name: Annotated[Optional[str], Field(alias="recipeName", description="食谱名称")] = None
    is_active: Annotated[bool, Field(alias="isActive", description="是否启用")] = True

    class Config:
        populate_by_name = True


class RecipeCreate(RecipeBase):
    """创建食谱模型"""
    meals: Annotated[List[RecipeMealCreate], Field(description="餐次列表")]


class RecipeUpdate(RecipeBase):
    """更新食谱模型"""
    id: Annotated[int, Field(description="食谱ID")]
    recipe_date: Annotated[Optional[date], Field(alias="recipeDate", description="食谱日期")] = None
    recipe_name: Annotated[Optional[str], Field(alias="recipeName", description="食谱名称")] = None
    is_active: Annotated[Optional[bool], Field(alias="isActive", description="是否启用")] = None
    meals: Annotated[Optional[List[RecipeMealUpdate]], Field(description="餐次列表")] = None


class RecipeSearch(BaseModel):
    """食谱搜索模型"""
    recipe_date: Annotated[Optional[date], Field(alias="recipeDate", description="食谱日期")] = None
    meal_type: Annotated[Optional[int], Field(alias="mealType", description="餐次类型")] = None
    current: Annotated[int, Field(description="页码")] = 1
    size: Annotated[int, Field(description="每页数量")] = 10

    class Config:
        populate_by_name = True


class RecipeResponse(RecipeBase):
    """食谱响应模型"""
    id: Annotated[int, Field(description="食谱ID")]
    meals: Annotated[List[dict], Field(description="餐次列表")]
    created_at: Annotated[Optional[str], Field(description="创建时间")] = None
    updated_at: Annotated[Optional[str], Field(description="更新时间")] = None

    class Config:
        populate_by_name = True


__all__ = [
    "RecipeMealBase", "RecipeMealCreate", "RecipeMealUpdate",
    "RecipeBase", "RecipeCreate", "RecipeUpdate", "RecipeSearch", "RecipeResponse"
]
