from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import date


class BaseDailyCostReport(BaseModel):
    report_date: date = Field(..., description="核算日期", example="2025-03-11")
    recipe_id: Optional[int] = Field(None, description="食谱ID")
    total_cost: float = Field(0.0, description="总成本")
    inventory_cost: float = Field(0.0, description="库存消耗成本")
    order_cost: float = Field(0.0, description="订单消耗成本")
    cost_details: List[Dict[str, Any]] = Field(default=[], description="成本明细")
    status: str = Field("draft", description="状态")
    remark: Optional[str] = Field(None, description="备注")


class DailyCostReportCreate(BaseDailyCostReport):
    pass


class DailyCostReportUpdate(BaseDailyCostReport):
    id: int

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})


class DailyCostReportSearch(BaseModel):
    report_date: Optional[date] = Field(None, description="核算日期")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    status: Optional[str] = Field(None, description="状态")
    current: int = Field(1, description="当前页")
    size: int = Field(10, description="每页数量")


class DailyCostReportGenerate(BaseModel):
    report_date: date = Field(..., description="核算日期")
    recipe_id: Optional[int] = Field(None, description="食谱ID，如果不指定则自动查找当日食谱")
