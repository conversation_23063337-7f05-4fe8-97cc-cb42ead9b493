from typing import Annotated

from pydantic import BaseModel, Field



class FoodStuffStoreBase(BaseModel):
    food_stuff_id: Annotated[int | None, Field(alias="foodStuffId", title="食材ID")] = None
    store_count: Annotated[float | None, Field(alias="storeCount", title="库存数量")] = None
    price: Annotated[float | None, Field(alias="price", title="单价")] = None
    school_id: Annotated[int | None, Field(alias="schoolId", title="学校ID")] = None
    supplier_id: Annotated[int | None, Field(alias="supplierId", title="供应商ID")] = None
    order_id: Annotated[int | None, Field(alias="orderId", title="订单ID")] = None
    recipe_id: Annotated[int | None, Field(alias="recipeId", title="食谱ID")] = None

    class Config:
        populate_by_name = True


class FoodStuffStoreCreate(FoodStuffStoreBase):
    food_stuff_id: Annotated[int, Field(alias="foodStuffId", title="食材ID")]
    store_count: Annotated[float, Field(alias="storeCount", title="库存数量")]
    price: Annotated[float, Field(alias="price", title="单价")]
    school_id: Annotated[int, Field(alias="schoolId", title="学校ID")]
    supplier_id: Annotated[int, Field(alias="supplierId", title="供应商ID")]


class FoodStuffStoreUpdate(FoodStuffStoreBase):
    id: Annotated[int, Field(alias="id", title="库存ID")]
    store_count: Annotated[float, Field(alias="storeCount", title="库存数量")]


class FoodStuffStoreSearch(FoodStuffStoreBase):
    current: Annotated[int | None, Field(description="页码")] = 1
    size: Annotated[int | None, Field(description="每页数量")] = 10
    food_stuff_name: Annotated[str | None, Field(description="食材名称")] = None


class FoodStuffStoreRecordBase(BaseModel):
    food_stuff_data: Annotated[list | None, Field(alias="foodStuffData", title="食材数据")] = None
    operation_type: Annotated[str | None, Field(alias="operationType", title="操作类型")] = None
    remark: Annotated[str | None, Field(alias="remark", title="操作备注")] = None
    school_id: Annotated[int | None, Field(alias="schoolId", title="学校ID")] = None
    operator_id: Annotated[int | None, Field(alias="operatorId", title="操作人ID")] = None

    class Config:
        populate_by_name = True

class FoodStuffStoreRecordCreate(FoodStuffStoreRecordBase):
    food_stuff_data: Annotated[list, Field(alias="foodStuffData", title="食材数据")]
    operation_type: Annotated[str, Field(alias="operationType", title="操作类型")]
    school_id: Annotated[int, Field(alias="schoolId", title="学校ID")]

class FoodStuffStoreRecordSearch(FoodStuffStoreRecordBase):
    current: Annotated[int | None, Field(description="页码")] = 1
    size: Annotated[int | None, Field(description="每页数量")] = 10
    start_date: Annotated[str | None, Field(description="开始日期")] = None
    end_date: Annotated[str | None, Field(description="结束日期")] = None

__all__ = ["FoodStuffStoreBase", "FoodStuffStoreCreate", "FoodStuffStoreUpdate", "FoodStuffStoreSearch", 
           "FoodStuffStoreRecordBase", "FoodStuffStoreRecordCreate", "FoodStuffStoreRecordSearch"]