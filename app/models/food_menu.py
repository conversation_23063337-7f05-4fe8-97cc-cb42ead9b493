from tortoise import fields
from enum import IntEnum
from .base import BaseModel, TimestampMixin

class MealType(IntEnum):
    BREAKFAST = 1
    MORNING_SNACK = 2
    LUNCH = 3
    AFTERNOON_SNACK = 4
    DINNER = 5


class Recipe(BaseModel, TimestampMixin):
    """食谱主表 - 按日期管理"""
    id = fields.IntField(pk=True, description="食谱ID")
    recipe_date = fields.DateField(description="食谱日期", index=True)
    recipe_name = fields.CharField(max_length=100, description="食谱名称", null=True)
    is_active = fields.BooleanField(default=True, description="是否启用")
    school_id = fields.IntField(description="学校ID", index=True)

    class Meta:
        table = "recipe"
        table_description = "食谱主表"
        unique_together = [("recipe_date", "school_id")]


class RecipeMeal(BaseModel, TimestampMixin):
    """食谱餐次表 - 管理具体餐次的菜品"""
    id = fields.IntField(pk=True, description="餐次ID")
    recipe = fields.ForeignKeyField("models.Recipe", related_name="meals", description="关联食谱")
    meal_type = fields.IntEnumField(MealType, description="餐次类型")
    dish_list = fields.JSONField(description="菜品列表")  # [{"dish_id": "1", "dish_name": "红烧肉", "ingredients": [...]}]
    is_active = fields.BooleanField(default=True, description="是否启用")

    class Meta:
        table = "recipe_meal"
        table_description = "食谱餐次表"
        unique_together = [("recipe_id", "meal_type")]



