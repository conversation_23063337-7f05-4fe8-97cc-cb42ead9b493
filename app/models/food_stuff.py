from enum import IntEnum
from tortoise import fields

from .base import BaseModel, TimestampMixin

class Unit(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="单位id")
    unit_name = fields.CharField(max_length=20, description="单位名称")

    class Meta:
        table = "unit"
        table_description = "单位"

class FoodStuffType(IntEnum):
    BULK_COMMODITY = 1  # 大宗商品
    RAW_MATERIAL = 2    # 原辅材料
    LOOSE_FOOD = 3      # 散货食材

class FoodStuff(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="食材id")
    food_stuff_name = fields.CharField(max_length=20, description="食材名称")
    food_stuff_type = fields.IntEnumField(enum_type=FoodStuffType, description="食材类型")
    food_stuff_unit_id = fields.IntField(description="计量单位ID")
    supplier_id = fields.IntField(description="供应商ID")
    nutrient_info = fields.JSONField(description="营养信息",default=[])#[{"nutrient_id": 1, "nutrient_name": "蛋白质", "nutrient_value": 100}]
    # 移除价格、生产日期、保质期字段，这些信息现在存储在订单明细中
    # food_stuff_price = fields.FloatField(description="食材价格", null=True)
    # production_date = fields.DatetimeField(description="生产日期", null=True)
    # shelf_life = fields.IntField(description="保质期(天)", null=True)


    class Meta:
        table = "food_stuff"
        table_description = "食材"


class Nutrient(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="营养素id")
    nutrient_name = fields.CharField(max_length=20, description="营养素名称")

    class Meta:
        table = "nutrient"
        table_description = "营养素"

class FoodStuffStore(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="食材库存id")
    food_stuff_id = fields.IntField(description="食材ID")
    store_count = fields.FloatField(description="库存数量")
    price = fields.FloatField(description="单价")
    school_id = fields.IntField(description="学校ID")
    supplier_id = fields.IntField(description="供应商ID")
    order_id = fields.IntField(description="订单ID", null=True)
    recipe_id = fields.IntField(description="食谱ID", null=True)

    class Meta:
        table = "food_stuff_store"
        table_description = "食材库存"

class FoodStuffStoreRecord(BaseModel, TimestampMixin):
    id = fields.IntField(pk=True, description="库存变化记录ID")
    food_stuff_data = fields.JSONField(description="所有食材数据", default=[])
    # 格式: [{"food_stuff_id": 1, "food_stuff_name": "大米", "before_count": 100, "change_count": 50, "after_count": 150, "unit_name": "公斤"}]
    operation_type = fields.CharField(max_length=20, description="操作类型")
    remark = fields.CharField(max_length=200, description="操作备注", null=True)
    school_id = fields.IntField(description="学校ID")
    operator_id = fields.IntField(description="操作人ID", null=True)
    
    class Meta:
        table = "food_stuff_store_record"
        table_description = "食材库存变化记录"


