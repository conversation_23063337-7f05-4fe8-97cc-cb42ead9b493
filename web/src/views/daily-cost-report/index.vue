<template>
  <CommonPage show-footer title="日成本核算单管理">
    <template #action>
      <NButton v-permission="'post/api/v1/daily-cost-report/generate'" type="primary" @click="handleAdd">
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />生成核算单
      </NButton>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getDailyCostReportList"
    >
      <template #queryBar>
        <QueryBarItem label="核算日期" :label-width="80">
          <NDatePicker
            v-model:value="queryItems.reportDate"
            type="date"
            placeholder="选择核算日期"
            clearable
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="日期范围" :label-width="80">
          <NDatePicker
            v-model:value="queryItems.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            clearable
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="80">
          <NSelect
            v-model:value="queryItems.status"
            placeholder="选择状态"
            clearable
            :options="statusOptions"
            style="width: 120px"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="generateRules"
      >
        <NFormItem label="核算日期" path="reportDate">
          <NDatePicker
            v-model:value="modalForm.reportDate"
            type="date"
            placeholder="选择核算日期"
            style="width: 100%"
            @update:value="checkDateAvailable"
          />
        </NFormItem>
        <NFormItem label="食谱" path="recipeId">
          <NSelect
            v-model:value="modalForm.recipeId"
            placeholder="选择食谱（留空自动查找当日食谱）"
            clearable
            :options="recipeOptions"
            :loading="recipeLoading"
          />
        </NFormItem>
        <NAlert v-if="dateCheckMessage" :type="dateCheckType" :title="dateCheckMessage" class="mb-4" />
      </NForm>
    </CrudModal>

    <!-- 详情模态框 -->
    <NModal v-model:show="showDetailModal" preset="card" title="核算单详情" style="width: 80%; max-width: 1200px">
      <div v-if="currentReport">
        <NDescriptions :column="3" bordered>
          <NDescriptionsItem label="核算日期">{{ currentReport.report_date }}</NDescriptionsItem>
          <NDescriptionsItem label="食谱名称">{{ currentReport.recipe_name || '无' }}</NDescriptionsItem>
          <NDescriptionsItem label="状态">
            <NTag :type="currentReport.status === 'confirmed' ? 'success' : 'warning'">
              {{ currentReport.status === 'confirmed' ? '已确认' : '草稿' }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem label="总成本">¥{{ currentReport.total_cost?.toFixed(2) || '0.00' }}</NDescriptionsItem>
          <NDescriptionsItem label="库存消耗成本">¥{{ currentReport.inventory_cost?.toFixed(2) || '0.00' }}</NDescriptionsItem>
          <NDescriptionsItem label="订单消耗成本">¥{{ currentReport.order_cost?.toFixed(2) || '0.00' }}</NDescriptionsItem>
        </NDescriptions>

        <NDivider>成本明细</NDivider>
        <NDataTable
          :columns="detailColumns"
          :data="currentReport.cost_details || []"
          :pagination="false"
        />
      </div>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NPopconfirm,
  NSelect,
  NTag,
  NDatePicker,
  NAlert,
  NModal,
  NDescriptions,
  NDescriptionsItem,
  NDivider,
  NDataTable,
  useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: '日成本核算单管理' })

const $message = useMessage()
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 详情模态框相关
const showDetailModal = ref(false)
const currentReport = ref(null)

// 日期检查相关
const dateCheckMessage = ref('')
const dateCheckType = ref('info')
const recipeOptions = ref([])
const recipeLoading = ref(false)

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '已确认', value: 'confirmed' }
]

// 使用CRUD composable
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd,
  handleDelete,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '日成本核算单',
  initForm: {
    reportDate: null,
    recipeId: null
  },
  doCreate: async (data) => {
    const params = {
      report_date: new Date(data.reportDate).toISOString().split('T')[0],
      recipe_id: data.recipeId || null
    }
    return await api.generateDailyCostReport(params)
  },
  doDelete: (data) => api.deleteDailyCostReport({ id: data.id }),
  refresh: () => $table.value?.handleSearch(),
})

// 日期检查方法
const checkDateAvailable = async (value) => {
  if (!value) {
    dateCheckMessage.value = ''
    return
  }

  try {
    const dateStr = new Date(value).toISOString().split('T')[0]
    const response = await api.checkDateAvailable({ report_date: dateStr })

    if (response.code === 200) {
      const data = response.data
      if (data.available) {
        dateCheckMessage.value = data.message
        dateCheckType.value = 'success'
        if (data.recipe_id) {
          modalForm.value.recipeId = data.recipe_id
          recipeOptions.value = [{ label: data.recipe_name, value: data.recipe_id }]
        }
      } else {
        dateCheckMessage.value = data.message
        dateCheckType.value = 'warning'
      }
      // 手动触发表单验证
      modalFormRef.value?.validate(['reportDate'])
    }
  } catch (error) {
    dateCheckMessage.value = '检查日期失败'
    dateCheckType.value = 'error'
  }
}

// 表单验证规则
const generateRules = {
  reportDate: [
    { 
      required: true, 
      message: '请选择核算日期', 
      trigger: ['change', 'blur'],
      validator: (rule, value) => {
        return !!value
      }
    }
  ]
}

// 表格列定义
const columns = [
  {
    title: '核算日期',
    key: 'report_date',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '食谱名称',
    key: 'recipe_name',
    width: 150,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.status === 'confirmed' ? 'success' : 'warning' },
        { default: () => row.status === 'confirmed' ? '已确认' : '草稿' }
      )
    }
  },
  {
    title: '总成本',
    key: 'total_cost',
    width: 120,
    align: 'center',
    render(row) {
      return `¥${row.total_cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '库存消耗',
    key: 'inventory_cost',
    width: 120,
    align: 'center',
    render(row) {
      return `¥${row.inventory_cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '订单消耗',
    key: 'order_cost',
    width: 120,
    align: 'center',
    render(row) {
      return `¥${row.order_cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.created_at))
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-right: 8px;',
              onClick: () => handleViewDetail(row),
            },
            {
              default: () => '查看详情',
              icon: renderIcon('material-symbols:visibility-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'get/api/v1/daily-cost-report/get']]
        ),
        row.status === 'draft' ? withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'success',
              style: 'margin-right: 8px;',
              onClick: () => handleConfirm(row),
            },
            {
              default: () => '确认',
              icon: renderIcon('material-symbols:check', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/daily-cost-report/confirm']]
        ) : null,
        row.status === 'draft' ? h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/daily-cost-report/delete']]
              ),
            default: () => h('div', {}, '确定删除该核算单吗?'),
          }
        ) : null
      ]
    }
  }
]

// 详情表格列定义
const detailColumns = [
  { title: '食材名称', key: 'food_stuff_name', width: 150 },
  { title: '单位', key: 'unit_name', width: 80 },
  {
    title: '库存消耗',
    key: 'inventory_consume',
    width: 200,
    render: (row) => {
      const consume = row.inventory_consume
      if (!consume || consume.quantity === 0) return '-'
      return `${consume.quantity}${row.unit_name} / ¥${consume.cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '订单消耗',
    key: 'order_consume',
    width: 200,
    render: (row) => {
      const consume = row.order_consume
      if (!consume || consume.quantity === 0) return '-'
      return `${consume.quantity}${row.unit_name} / ¥${consume.cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '总消耗成本',
    key: 'total_cost',
    width: 120,
    render: (row) => {
      const inventoryCost = row.inventory_consume?.cost || 0
      const orderCost = row.order_consume?.cost || 0
      return `¥${(inventoryCost + orderCost).toFixed(2)}`
    }
  }
]

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await api.getDailyCostReportById({ id: row.id })
    if (response.code === 200) {
      currentReport.value = response.data
      showDetailModal.value = true
    }
  } catch (error) {
    $message.error('获取详情失败')
  }
}

// 确认核算单
const handleConfirm = async (row) => {
  try {
    const response = await api.confirmDailyCostReport({ id: row.id })
    if (response.code === 200) {
      $message.success('确认成功')
      $table.value?.handleSearch()
    } else {
      $message.error(response.msg || '确认失败')
    }
  } catch (error) {
    $message.error('确认失败')
  }
}

// 初始化数据
onMounted(() => {
  $table.value?.handleSearch()
})
</script>


