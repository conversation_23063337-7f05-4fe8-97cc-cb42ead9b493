<template>
  <div class="daily-cost-report">
    <n-card title="日成本核算单管理">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="showGenerateModal = true">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            生成核算单
          </n-button>
        </n-space>
      </template>

      <!-- 搜索区域 -->
      <n-space class="mb-4">
        <n-date-picker
          v-model:value="searchForm.reportDate"
          type="date"
          placeholder="选择核算日期"
          clearable
        />
        <n-date-picker
          v-model:value="searchForm.dateRange"
          type="daterange"
          placeholder="选择日期范围"
          clearable
        />
        <n-select
          v-model:value="searchForm.status"
          placeholder="选择状态"
          clearable
          :options="statusOptions"
          style="width: 120px"
        />
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button @click="handleReset">重置</n-button>
      </n-space>

      <!-- 表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 生成核算单模态框 -->
    <n-modal v-model:show="showGenerateModal" preset="dialog" title="生成日成本核算单">
      <n-form ref="generateFormRef" :model="generateForm" :rules="generateRules">
        <n-form-item label="核算日期" path="reportDate">
          <n-date-picker
            v-model:value="generateForm.reportDate"
            type="date"
            placeholder="选择核算日期"
            style="width: 100%"
            @update:value="checkDateAvailable"
          />
        </n-form-item>
        <n-form-item label="食谱" path="recipeId">
          <n-select
            v-model:value="generateForm.recipeId"
            placeholder="选择食谱（留空自动查找当日食谱）"
            clearable
            :options="recipeOptions"
            :loading="recipeLoading"
          />
        </n-form-item>
        <n-alert v-if="dateCheckMessage" :type="dateCheckType" :title="dateCheckMessage" class="mb-4" />
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showGenerateModal = false">取消</n-button>
          <n-button type="primary" @click="handleGenerate" :loading="generateLoading">生成</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 详情模态框 -->
    <n-modal v-model:show="showDetailModal" preset="card" title="核算单详情" style="width: 80%; max-width: 1200px">
      <div v-if="currentReport">
        <n-descriptions :column="3" bordered>
          <n-descriptions-item label="核算日期">{{ currentReport.report_date }}</n-descriptions-item>
          <n-descriptions-item label="食谱名称">{{ currentReport.recipe_name || '无' }}</n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="currentReport.status === 'confirmed' ? 'success' : 'warning'">
              {{ currentReport.status === 'confirmed' ? '已确认' : '草稿' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="总成本">¥{{ currentReport.total_cost?.toFixed(2) || '0.00' }}</n-descriptions-item>
          <n-descriptions-item label="库存消耗成本">¥{{ currentReport.inventory_cost?.toFixed(2) || '0.00' }}</n-descriptions-item>
          <n-descriptions-item label="订单消耗成本">¥{{ currentReport.order_cost?.toFixed(2) || '0.00' }}</n-descriptions-item>
        </n-descriptions>

        <n-divider>成本明细</n-divider>
        <n-data-table
          :columns="detailColumns"
          :data="currentReport.cost_details || []"
          :pagination="false"
        />
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { NButton, NTag, NSpace, useMessage } from 'naive-ui'
import { AddOutline } from '@vicons/ionicons5'
import { api } from '@/api'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const generateLoading = ref(false)
const recipeLoading = ref(false)
const showGenerateModal = ref(false)
const showDetailModal = ref(false)
const tableData = ref([])
const currentReport = ref(null)
const generateFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  reportDate: null,
  dateRange: null,
  status: null,
  current: 1,
  size: 10
})

// 生成表单
const generateForm = reactive({
  reportDate: null,
  recipeId: null
})

// 日期检查相关
const dateCheckMessage = ref('')
const dateCheckType = ref('info')
const recipeOptions = ref([])

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '已确认', value: 'confirmed' }
]

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

// 表格列定义
const columns = [
  { title: '核算日期', key: 'report_date', width: 120 },
  { title: '食谱名称', key: 'recipe_name', width: 150 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return h(NTag, {
        type: row.status === 'confirmed' ? 'success' : 'warning'
      }, {
        default: () => row.status === 'confirmed' ? '已确认' : '草稿'
      })
    }
  },
  { title: '总成本', key: 'total_cost', width: 120, render: (row) => `¥${row.total_cost?.toFixed(2) || '0.00'}` },
  { title: '库存消耗', key: 'inventory_cost', width: 120, render: (row) => `¥${row.inventory_cost?.toFixed(2) || '0.00'}` },
  { title: '订单消耗', key: 'order_cost', width: 120, render: (row) => `¥${row.order_cost?.toFixed(2) || '0.00'}` },
  { title: '创建时间', key: 'created_at', width: 160, render: (row) => new Date(row.created_at).toLocaleString() },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row) => {
      return h(NSpace, null, {
        default: () => [
          h(NButton, {
            size: 'small',
            onClick: () => handleViewDetail(row)
          }, { default: () => '查看详情' }),
          row.status === 'draft' ? h(NButton, {
            size: 'small',
            type: 'success',
            onClick: () => handleConfirm(row)
          }, { default: () => '确认' }) : null,
          row.status === 'draft' ? h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          }, { default: () => '删除' }) : null
        ]
      })
    }
  }
]

// 详情表格列定义
const detailColumns = [
  { title: '食材名称', key: 'food_stuff_name', width: 150 },
  { title: '单位', key: 'unit_name', width: 80 },
  {
    title: '库存消耗',
    key: 'inventory_consume',
    width: 200,
    render: (row) => {
      const consume = row.inventory_consume
      if (!consume || consume.quantity === 0) return '-'
      return `${consume.quantity}${row.unit_name} / ¥${consume.cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '订单消耗',
    key: 'order_consume',
    width: 200,
    render: (row) => {
      const consume = row.order_consume
      if (!consume || consume.quantity === 0) return '-'
      return `${consume.quantity}${row.unit_name} / ¥${consume.cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '总消耗成本',
    key: 'total_cost',
    width: 120,
    render: (row) => {
      const inventoryCost = row.inventory_consume?.cost || 0
      const orderCost = row.order_consume?.cost || 0
      return `¥${(inventoryCost + orderCost).toFixed(2)}`
    }
  }
]

// 表单验证规则
const generateRules = {
  reportDate: [
    { required: true, message: '请选择核算日期', trigger: 'change' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.page,
      size: pagination.pageSize,
      ...searchForm
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = new Date(searchForm.dateRange[0]).toISOString().split('T')[0]
      params.end_date = new Date(searchForm.dateRange[1]).toISOString().split('T')[0]
      delete params.dateRange
    }
    
    // 处理单个日期
    if (searchForm.reportDate) {
      params.report_date = new Date(searchForm.reportDate).toISOString().split('T')[0]
    }
    
    const response = await api.getDailyCostReportList(params)
    if (response.code === 200) {
      tableData.value = response.data
      pagination.itemCount = response.total
    }
  } catch (error) {
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    reportDate: null,
    dateRange: null,
    status: null
  })
  pagination.page = 1
  fetchData()
}

const handlePageChange = (page) => {
  pagination.page = page
  fetchData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchData()
}

const checkDateAvailable = async (value) => {
  if (!value) {
    dateCheckMessage.value = ''
    return
  }
  
  try {
    const dateStr = new Date(value).toISOString().split('T')[0]
    const response = await api.checkDateAvailable({ report_date: dateStr })
    
    if (response.code === 200) {
      const data = response.data
      if (data.available) {
        dateCheckMessage.value = data.message
        dateCheckType.value = 'success'
        if (data.recipe_id) {
          generateForm.recipeId = data.recipe_id
          recipeOptions.value = [{ label: data.recipe_name, value: data.recipe_id }]
        }
      } else {
        dateCheckMessage.value = data.message
        dateCheckType.value = 'warning'
      }
    }
  } catch (error) {
    dateCheckMessage.value = '检查日期失败'
    dateCheckType.value = 'error'
  }
}

const handleGenerate = async () => {
  try {
    await generateFormRef.value?.validate()
    generateLoading.value = true
    
    const params = {
      report_date: new Date(generateForm.reportDate).toISOString().split('T')[0],
      recipe_id: generateForm.recipeId || null
    }
    
    const response = await api.generateDailyCostReport(params)
    if (response.code === 200) {
      message.success('生成核算单成功')
      showGenerateModal.value = false
      Object.assign(generateForm, { reportDate: null, recipeId: null })
      dateCheckMessage.value = ''
      fetchData()
    } else {
      message.error(response.msg || '生成核算单失败')
    }
  } catch (error) {
    // 验证失败或其他错误
  } finally {
    generateLoading.value = false
  }
}

const handleViewDetail = async (row) => {
  try {
    const response = await api.getDailyCostReportById({ id: row.id })
    if (response.code === 200) {
      currentReport.value = response.data
      showDetailModal.value = true
    }
  } catch (error) {
    message.error('获取详情失败')
  }
}

const handleConfirm = async (row) => {
  try {
    const response = await api.confirmDailyCostReport({ id: row.id })
    if (response.code === 200) {
      message.success('确认成功')
      fetchData()
    } else {
      message.error(response.msg || '确认失败')
    }
  } catch (error) {
    message.error('确认失败')
  }
}

const handleDelete = async (row) => {
  try {
    const response = await api.deleteDailyCostReport({ id: row.id })
    if (response.code === 200) {
      message.success('删除成功')
      fetchData()
    } else {
      message.error(response.msg || '删除失败')
    }
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.daily-cost-report {
  padding: 16px;
}
</style>
