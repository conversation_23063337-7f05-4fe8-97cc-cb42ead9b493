<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, computed } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NPopconfirm,
  NTag,
  NSelect,
  NSpace,
  NDataTable,
  NBadge,
  NModal,
  NList,
  NListItem,
  NEmpty,
  NCard,
  NInputGroup,
  NStatistic
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'
import { useCartStore } from '@/store' // 导入购物车store

defineOptions({ name: '食材管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 使用购物车store
const cartStore = useCartStore()

// 购物车弹窗相关
const showCartModal = ref(false)

// 打开购物车弹窗
function openCartModal() {
  showCartModal.value = true
}

// 关闭购物车弹窗
function closeCartModal() {
  showCartModal.value = false
}

// 完成下单
async function completeOrder() {
  try {
    // 准备订单数据
    const orderData = {
      orderItems: cartStore.cartItems.map(item => ({
        id: item.id,
        name: item.name,
        type: item.type,
        quantity: item.quantity,
        unitId: item.unitId,
        unitName: item.unitName,
        supplierId: item.supplierId,
        supplierName: item.supplierName
      })),
      orderRemark: '从购物车直接下单'
    }

    // 调用直接下单API
    await api.createDirectOrder(orderData)

    // 清空购物车
    cartStore.clearCart()
    // 关闭弹窗
    showCartModal.value = false
    // 显示成功提示
    $message.success('下单成功')
  } catch (error) {
    console.error('下单失败:', error)
    $message.error('下单失败，请重试')
  }
}

// 食材类型选项
const foodStuffTypeOptions = [
  { label: '大宗商品', value: 1 },
  { label: '原辅材料', value: 2 },
  { label: '散货食材', value: 3 }
]

// 单位列表
const unitOptions = ref([])
// 营养素列表
const nutrientOptions = ref([])
// 供应商列表
const supplierOptions = ref([])

// 获取单位和营养素列表
async function fetchOptions() {
  try {
    // 加载单位列表
    const unitRes = await api.getUnitList({ page: 1, size: 9999 })
    if (unitRes && unitRes.data) {
      unitOptions.value = unitRes.data.map(item => ({
        label: item.unit_name,
        value: item.id
      }))
    }

    // 加载营养素列表
    const nutrientRes = await api.getNutrientList({ page: 1, size: 9999 })
    if (nutrientRes && nutrientRes.data) {
      nutrientOptions.value = nutrientRes.data.map(item => ({
        label: item.nutrient_name,
        value: item.id
      }))
    }

    // 加载供应商列表 (需要实现相关API)
    const supplierRes = await api.getSupplierList({ page: 1, size: 9999 })
    if (supplierRes && supplierRes.data) {
      supplierOptions.value = supplierRes.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

const {
  modalVisible,
  modalAction,
  modalTitle,
  modalLoading,
  handleAdd: originalHandleAdd,
  handleDelete,
  handleEdit: originalHandleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '食材',
  initForm: {
    foodStuffName: '',
    foodStuffType: 1,
    foodStuffUnitId: null,
    foodStuffPrice: 0,
    supplierId: null,
    nutrientInfo: []
  },
  doCreate: api.createFoodStuff,
  doDelete: api.deleteFoodStuff,
  doUpdate: api.updateFoodStuff,
  refresh: () => $table.value?.handleSearch(),
})

// 重写handleAdd和handleEdit方法，在打开弹窗前刷新选项数据
const handleAdd = async () => {
  await fetchOptions() // 刷新选项数据
  originalHandleAdd()
}

const handleEdit = async (row) => {
  await fetchOptions() // 刷新选项数据
  // 将API返回的字段名映射到表单字段名
  const formData = {
    id: row.id,
    foodStuffName: row.food_stuff_name,
    foodStuffType: row.food_stuff_type,
    foodStuffUnitId: row.food_stuff_unit_id,
    foodStuffPrice: row.food_stuff_price,
    supplierId: row.supplier_id,
    nutrientInfo: row.nutrient_info || []
  }
  originalHandleEdit(formData)
}

// 当前编辑的营养素信息
const selectedNutrients = ref([])
const nutrientValue = ref('')
const currentNutrient = ref(null)

// 添加营养素
function addNutrient() {
  if (!currentNutrient.value || !nutrientValue.value) return

  const nutrientName = nutrientOptions.value.find(n => n.value === currentNutrient.value)?.label

  modalForm.nutrientInfo = modalForm.nutrientInfo || []
  modalForm.nutrientInfo.push({
    nutrient_id: currentNutrient.value,
    nutrient_name: nutrientName,
    nutrient_value: parseFloat(nutrientValue.value)
  })

  currentNutrient.value = null
  nutrientValue.value = ''
}

// 删除营养素
function removeNutrient(index) {
  modalForm.nutrientInfo.splice(index, 1)
}

// 营养素表格列定义
const nutrientColumns = [
  {
    title: '营养素名称',
    key: 'nutrient_name',
    align: 'center'
  },
  {
    title: '含量',
    key: 'nutrient_value',
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row, index) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          onClick: () => removeNutrient(index)
        },
        { default: () => '删除' }
      )
    }
  }
]

onMounted(() => {
  $table.value?.handleSearch()
  fetchOptions()
})

// 购物车表格列定义
const cartColumns = [
  {
    title: '食材名称',
    key: 'name',
    width: 150,
    align: 'center'
  },
  {
    title: '数量',
    key: 'quantity',
    width: 150,
    align: 'center',
    render(row) {
      return h(NInputNumber, {
        value: row.quantity,
        min: 1,
        precision: 2,
        step: 0.5,
        style: 'width: 100px',
        onUpdateValue: (value) => {
          // 直接更新购物车中的数量
          row.quantity = value
          // 保存到本地存储
          cartStore.saveToLocalStorage()
        }
      })
    }
  },
  {
    title: '单位',
    key: 'unitName',
    width: 100,
    align: 'center'
  },
  {
    title: '供应商',
    key: 'supplierName',
    width: 150,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'error',
          onClick: () => cartStore.removeFromCart(row.id)
        },
        { default: () => '移除' }
      )
    }
  }
]

const columns = [
  {
    title: '食材名称',
    key: 'food_stuff_name',
    width: 100,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '食材类型',
    key: 'food_stuff_type',
    width: 100,
    align: 'center',
    render(row) {
      const typeMap = {
        1: { label: '大宗商品', type: 'success' },
        2: { label: '原辅材料', type: 'info' },
        3: { label: '散货食材', type: 'warning' }
      }
      const type = typeMap[row.food_stuff_type] || { label: '未知', type: 'default' }
      return h(NTag, { type: type.type }, { default: () => type.label })
    }
  },
  {
    title: '单位',
    key: 'unit_name',
    width: 80,
    align: 'center',
    render(row) {
      return h('span', unitOptions.value.find(item => item.value === row.food_stuff_unit_id)?.label)
    }
  },
  // {
  //   title: '价格',
  //   key: 'food_stuff_price',
  //   width: 80,
  //   align: 'center',
  //   render(row) {
  //     return h('span', `¥${row.food_stuff_price?.toFixed(2)}`)
  //   }
  // },
  {
    title: '供应商',
    key: 'supplier_name',
    width: 100,
    align: 'center',
    render(row) {
      return h('span', supplierOptions.value.find(item => item.value === row.supplier_id)?.label)
    }
  },
  {
    title: '创建日期',
    key: 'created_at',
    width: 120,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.created_at))
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-right: 8px;',
              onClick: () => {
                cartStore.addToCart(row, unitOptions.value, supplierOptions.value)
                $message.success('已添加到购物车')
              },
            },
            {
              default: () => '购物车',
              icon: renderIcon('tabler:plus', { size: 16 }),
            }
          ),
          [[vPermission, 'get/api/v1/food-stuff/list']]
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 8px;',
              onClick: () => {
                handleEdit(row)
              },
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/food-stuff/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-right: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/food-stuff/delete']]
              ),
            default: () => h('div', {}, '确定删除该食材吗?'),
          }
        )
      ]
    }
  }
]
</script>

<template>
  <CommonPage show-footer title="食材管理">
    <template #action>
      <NSpace>
        <NBadge :value="cartStore.cartCount" :max="99" :show-zero="false" processing>
          <NButton type="info" @click="openCartModal">
            <TheIcon icon="material-symbols:shopping-cart-outline" :size="18" class="mr-5" />购物车
          </NButton>
        </NBadge>
        <NButton v-permission="'post/api/v1/food-stuff/create'" type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增食材
        </NButton>
      </NSpace>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getFoodStuffList"
    >
      <template #queryBar>
        <QueryBarItem label="食材名称" :label-width="70">
          <NInput
            v-model:value="queryItems.foodStuffName"
            clearable
            type="text"
            placeholder="请输入食材名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="食材类型" :label-width="70">
          <NSelect
            style="width: 150px;"
            v-model:value="queryItems.foodStuffType"
            clearable
            :options="foodStuffTypeOptions"
            placeholder="请选择食材类型"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <NFormItem
          label="食材名称"
          path="foodStuffName"
          :rule="{
            required: true,
            message: '请输入食材名称',
            trigger: ['input', 'blur'],
          }"
        >
          <NInput v-model:value="modalForm.foodStuffName" placeholder="请输入食材名称" />
        </NFormItem>

        <NFormItem
          label="食材类型"
          path="foodStuffType"
          :rule="{
            required: true,
            message: '请选择食材类型',
            trigger: ['change', 'blur'],
            type: 'number',
          }"
        >
          <NSelect
            v-model:value="modalForm.foodStuffType"
            :options="foodStuffTypeOptions"
            placeholder="请选择食材类型"
          />
        </NFormItem>

        <NFormItem
          label="计量单位"
          path="foodStuffUnitId"
          :rule="{
            required: true,
            message: '请选择计量单位',
            trigger: ['change', 'blur'],
            type: 'number',
          }"
        >
          <NSelect
            v-model:value="modalForm.foodStuffUnitId"
            :options="unitOptions"
            placeholder="请选择计量单位"
          />
        </NFormItem>

        <NFormItem
          label="供应商"
          path="supplierId"
          :rule="{
            required: true,
            message: '请选择供应商',
            trigger: ['change', 'blur'],
            type: 'number',
          }"
        >
          <NSelect
            v-model:value="modalForm.supplierId"
            :options="supplierOptions"
            placeholder="请选择供应商"
          />
        </NFormItem>
      </NForm>
    </CrudModal>

    <NModal
      v-model:show="showCartModal"
      preset="card"
      title="购物车"
      style="width: 100%"
      :mask-closable="true"
      size="huge"
      :bordered="false"
    >
      <div style="padding: 16px 0">
        <div v-if="cartStore.cartItems.length === 0">
          <NEmpty description="购物车为空" />
        </div>
        <div v-else>
          <NDataTable
            :columns="cartColumns"
            :data="cartStore.cartItems"
            :bordered="false"
            size="small"
            :pagination="{
              pageSize: 10
            }"
          />
        </div>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <NSpace>
            <NButton @click="closeCartModal">取消</NButton>
            <NPopconfirm
              positive-text="确认"
              negative-text="取消"
              @positive-click="completeOrder"
              placement="top"
            >
              <template #trigger>
                <NButton type="primary" :disabled="cartStore.cartItems.length === 0">确认下单</NButton>
              </template>
              确认要提交这个订单吗？
            </NPopconfirm>
          </NSpace>
        </div>
      </template>
    </NModal>
  </CommonPage>
</template>