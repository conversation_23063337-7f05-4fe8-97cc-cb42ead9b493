<template>
  <CommonPage show-footer title="食材库存记录">
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getFoodStuffStoreRecords"
    >
      <template #queryBar>
        <QueryBarItem label="操作类型" :label-width="70">
          <NSelect
            v-model:value="queryItems.operationType"
            :options="operationTypeOptions"
            placeholder="请选择操作类型"
            clearable
            style="width: 150px;"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="开始日期" :label-width="70">
          <NDatePicker
            v-model:value="queryItems.startDate"
            type="date"
            placeholder="选择开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 150px;"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="结束日期" :label-width="70">
          <NDatePicker
            v-model:value="queryItems.endDate"
            type="date"
            placeholder="选择结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 150px;"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NTag,
  NCollapse,
  NCollapseItem,
  NDescriptions,
  NDescriptionsItem,
  NSelect,
  NDatePicker
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: '食材库存记录' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

const operationTypeOptions = [
  { label: '入库', value: '入库' },
  { label: '手动调整', value: '手动调整' },
  { label: '下单库存消耗', value: '下单库存消耗' }
]

const columns = [
  {
    title: '操作类型',
    key: 'operation_type',
    width: 120,
    align: 'center',
    render(row) {
      const typeMap = {
        '入库': 'info',
        '手动调整': 'warning',
        '下单库存消耗': 'error'
      }
      return h(NTag, { type: typeMap[row.operation_type] || 'default' }, { default: () => row.operation_type })
    }
  },
  {
    title: '涉及食材数量',
    key: 'foodStuffCount',
    width: 120,
    align: 'center',
    render(row) {
      return row.food_stuff_data?.length || 0
    }
  },
  {
    title: '食材详情',
    key: 'food_stuff_data',
    width: 300,
    align: 'center',
    render(row) {
      if (!row.food_stuff_data || row.food_stuff_data.length === 0) {
        return '无数据'
      }
      
      return h(NCollapse, { size: 'small' }, {
        default: () => h(NCollapseItem, { title: '查看详情', name: '1' }, {
          default: () => h(NDescriptions, { bordered: true, column: 2, size: 'small' }, {
            default: () => row.food_stuff_data.map((item, index) => [
              h(NDescriptionsItem, { label: `食材${index + 1}` }, { default: () => item.food_stuff_name }),
              h(NDescriptionsItem, { label: '变化前' }, { default: () => `${item.before_count?.toFixed(2) || '0.00'} ${item.unit_name || ''}` }),
              h(NDescriptionsItem, { label: '变化量' }, { 
                default: () => {
                  const change = item.change_count || 0
                  const text = change >= 0 ? `+${change.toFixed(2)}` : change.toFixed(2)
                  return h(NTag, { type: change >= 0 ? 'success' : 'error', size: 'small' }, { default: () => `${text} ${item.unit_name || ''}` })
                }
              }),
              h(NDescriptionsItem, { label: '变化后' }, { default: () => `${item.after_count?.toFixed(2) || '0.00'} ${item.unit_name || ''}` })
            ]).flat()
          })
        })
      })
    }
  },
  {
    title: '备注',
    key: 'remark',
    width: 200,
    align: 'center',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '记录时间',
    key: 'createdAt',
    width: 180,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.createdAt))
    }
  }
]

onMounted(() => {
  $table.value?.handleSearch()
})
</script>