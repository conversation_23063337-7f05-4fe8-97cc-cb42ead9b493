<template>
  <CommonPage show-footer title="食材库存管理">
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getFoodStuffStoreList"
    >
      <template #queryBar>
        <QueryBarItem label="食材名称" :label-width="70">
          <NInput
            v-model:value="queryItems.foodStuffName"
            clearable
            type="text"
            placeholder="请输入食材名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="价格范围" :label-width="70">
          <NSpace>
            <NInputNumber
              v-model:value="queryItems.minPrice"
              placeholder="最低价格"
              :min="0"
              :precision="2"
              style="width: 120px;"
              @update:value="$table?.handleSearch()"
            />
            <span>-</span>
            <NInputNumber
              v-model:value="queryItems.maxPrice"
              placeholder="最高价格"
              :min="0"
              :precision="2"
              style="width: 120px;"
              @update:value="$table?.handleSearch()"
            />
          </NSpace>
        </QueryBarItem>
      </template>
    </CrudTable>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref } from 'vue'
import { NInputNumber, NSpace } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate } from '@/utils'
import api from '@/api'

defineOptions({ name: '食材库存管理' })

const $table = ref(null)
const queryItems = ref({})

const columns = [
  {
    title: '食材名称',
    key: 'foodStuffName',
    width: 140,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '供应商名称',
    key: 'supplierName',
    width: 130,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '库存数量',
    key: 'storeCount',
    width: 100,
    align: 'center',
    render(row) {
      return h('span', `${row.storeCount}`)
    }
  },
  {
    title: '单位',
    key: 'unitName',
    width: 80,
    align: 'center'
  },
  {
    title: '单价',
    key: 'price',
    width: 100,
    align: 'center',
    render(row) {
      return h('span', `¥${(row.price || 0).toFixed(2)}`)
    }
  },
  {
    title: '总价值',
    key: 'totalValue',
    width: 120,
    align: 'center',
    render(row) {
      const totalValue = (row.storeCount || 0) * (row.price || 0)
      return h('span', {
        style: { color: '#f56c6c', fontWeight: 'bold' }
      }, `¥${totalValue.toFixed(2)}`)
    }
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 160,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.updatedAt))
    }
  }
]

// 库存数量现在只显示，不支持直接修改

onMounted(() => {
  $table.value?.handleSearch()
})
</script>