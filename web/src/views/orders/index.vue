<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
    NButton,
    NForm,
    NFormItem,
    NInput,
    NModal,
    NPopconfirm,
    NSelect,
    NDatePicker,
    NTag,
    NInputNumber,
    NList,
    NListItem,
    NThing,
    NSpace,
    NQrCode,
    useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import { useUserStore } from '@/store'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'
import QrcodeVue from 'qrcode.vue'

import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import printJS from 'print-js'

defineOptions({ name: '订单管理' })

const $table = ref(null)
const queryItems = ref({})
const supplierOptions = ref([])
const vPermission = resolveDirective('permission')
const printModalVisible = ref(false)
const detailModalVisible = ref(false)
const currentOrder = ref(null)
const orderDetail = ref(null)
const supplierInfo = ref(null)
const message = useMessage()
const userStore = useUserStore()

// 订单类型选项
const orderTypeOptions = [
    { label: '大宗商品', value: 1 },
    { label: '原辅材料', value: 2 },
    { label: '散货食材', value: 3 }
]

// 订单状态选项
const orderStatusOptions = [
    { label: '待处理', value: 1 },
    { label: '已出库', value: 2 },
    { label: '已入库', value: 3 },
    { label: '已支付', value: 4 },
    { label: '已取消', value: 5 }
]

// 获取供应商列表
const getSupplierOptions = async () => {
    try {
        const res = await api.getSuppliers()
        if (res.code === 200) {
            supplierOptions.value = res.data.map(item => ({
                label: item.name,
                value: item.id
            }))
        }
    } catch (error) {
        console.error('获取供应商列表失败:', error)
    }
}

const {
    modalVisible,
    modalAction,
    modalTitle,
    modalLoading,
    handleAdd,
    handleDelete,
    handleEdit,
    handleSave,
    modalForm,
    modalFormRef,
} = useCRUD({
    name: '订单',
    initForm: {
        orderItems: [],
    },
    doCreate: api.createOrder,
    doDelete: api.deleteOrder,
    doUpdate: api.updateOrder,
    refresh: () => $table.value?.handleSearch(),
})

const formRules = {
    orderItems: {
        validator: (rule, value) => {
            if (!value || value.length === 0) {
                return new Error('订单明细不能为空')
            }
            
            // 检查每个订单食材的价格
            for (const item of value) {
                if (item.price === undefined || item.price === null) {
                    return new Error(`${item.name} 的价格不能为空`)
                }
                
                if (item.price <= 0) {
                    return new Error(`${item.name} 的价格必须大于0`)
                }
                
                // 检查小数位数是否超过2位
                const priceStr = item.price.toString()
                if (priceStr.includes('.')) {
                    const decimalPlaces = priceStr.split('.')[1].length
                    if (decimalPlaces > 2) {
                        return new Error(`${item.name} 的价格小数位不能超过2位`)
                    }
                }
            }
            
            return true
        }
    }
}

// 获取供应商详细信息
const getSupplierInfo = async (supplierId) => {
    try {
        const res = await api.getDept({ id: supplierId })
        if (res.code === 200) {
            return res.data
        }
    } catch (error) {
        console.error('获取供应商信息失败:', error)
    }
    return null
}

const handleShowShipment = async (row) => {
    modalVisible.value = true
    modalForm.value.orderItems = row.order_items
    modalForm.value.id = row.id
    currentOrder.value = row
    
    // 获取供应商详细信息
    if (row.supplier_id) {
        supplierInfo.value = await getSupplierInfo(row.supplier_id)
    }
}

const handleShipment = async () => {
    // 验证表单
    try {
        await modalFormRef.value?.validate()
        
        const res = await api.shipment({order_id: modalForm.value.id, order_items: modalForm.value.orderItems})
        if (res.code === 200) {
            modalVisible.value = false
            $table.value?.handleSearch()
            
            printModalVisible.value = true
        } else {
            message.error(res.msg)
        }
    } catch (e) {
        // 验证失败，错误信息已由表单组件显示
        console.error('表单验证失败', e)
    }
}

// 查看订单详情
const handleViewDetail = async (row) => {
    try {
        const res = await api.getOrderDetail({ order_id: row.id })
        if (res.code === 200) {
            orderDetail.value = res.data
            detailModalVisible.value = true
        } else {
            message.error(res.msg || '获取订单详情失败')
        }
    } catch (error) {
        console.error('获取订单详情失败:', error)
        message.error('获取订单详情失败')
    }
}

// 打印订单方法
const printOrder = async () => {
    try {
        // 先等待一下二维码渲染完成
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const element = document.getElementById('printArea')


        const canvas = await html2canvas(element, {
            useCORS: true,  // 允许跨域图片
            allowTaint: true, // 允许加载跨域图片
            scale: 2, // 提高清晰度
            logging: false,
            onclone: (clonedDoc) => {
                // 确保克隆的文档中二维码已经完全渲染
                const qrCodeElement = clonedDoc.querySelector('#printArea .n-qrcode')
                if (qrCodeElement) {
                    // 给克隆的二维码元素添加足够的时间完成渲染
                    return new Promise(resolve => setTimeout(resolve, 300))
                }
                return Promise.resolve()
            }
        })
        const imgData = canvas.toDataURL('image/png')
        // // 导出图片
        // const link = document.createElement('a')
        // link.href = imgData
        // link.download = `订单_${currentOrder.value?.order_number || '未知'}_${new Date().toISOString().slice(0, 10)}.png`
        // document.body.appendChild(link)
        // link.click()
        // document.body.removeChild(link)
        
        // message.success('图片导出成功')

        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm'
        })
        // 计算宽高比以确保图像不会被拉伸
        const imgWidth = pdf.internal.pageSize.getWidth()
        const imgHeight = (canvas.height * imgWidth) / canvas.width
        
        // 添加图像到PDF
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight)
        
        // 如果内容超过一页，自动添加新页
        if (imgHeight > pdf.internal.pageSize.getHeight()) {
            let heightLeft = imgHeight
            let position = 0
            
            // 移除之前添加的图像
            pdf.deletePage(1)
            
            // 逐页添加内容
            while (heightLeft > 0) {
                pdf.addPage()
                pdf.addImage(imgData, 'PNG', 0, -position, imgWidth, imgHeight)
                heightLeft -= pdf.internal.pageSize.getHeight()
                position += pdf.internal.pageSize.getHeight()
            }
        }
        
        // 生成PDF并下载
        // pdf.save(`订单_${currentOrder.value?.order_number || '未知'}_${new Date().toISOString().slice(0, 10)}.pdf`)
        
        // 也可以直接打印
        const pdfBlob = pdf.output('bloburl')
        printJS({
            printable: pdfBlob,
            type: 'pdf',
        })
        
        
        // 打印完成后关闭预览
        printModalVisible.value = false
    } catch (error) {
        console.error('打印失败:', error)
        message.error('打印失败: ' + error.message)
    }
}

onMounted(() => {
    // 只有非供应商角色才需要获取供应商列表
    const userInfo = userStore.userInfo
    if (userInfo && userInfo.roles && userInfo.roles.length > 0) {
        // 检查用户是否为供应商角色（type为2）
        const isSupplier = userInfo.roles.some(role => role.name.includes('供应商'))
        if (!isSupplier) {
            getSupplierOptions()
        }
    } else {
        // 如果没有角色信息，默认获取供应商列表
        getSupplierOptions()
    }
    $table.value?.handleSearch()
})

const columns = [
    {
        title: '订单编号',
        key: 'order_number',
        width: 150,
        align: 'center'
    },
    {
        title: '订单类型',
        key: 'order_type',
        width: 120,
        align: 'center',
        render(row) {
            const type = orderTypeOptions.find(item => item.value === row.order_type)
            let tagType = 'default'
            switch (row.order_type) {
                case 1: tagType = 'info'; break
                case 2: tagType = 'warning'; break
                case 3: tagType = 'success'; break
            }
            return type ? h(NTag, { type: tagType }, { default: () => type.label }) : h('span', '')
        }
    },
    {
        title: '订单状态',
        key: 'order_status',
        width: 120,
        align: 'center',
        render(row) {
            const status = orderStatusOptions.find(item => item.value === row.order_status)
            let tagType = 'default'
            switch (row.order_status) {
                case 1: tagType = 'warning'; break
                case 2: tagType = 'info'; break
                case 3: tagType = 'success'; break
                case 4: tagType = 'success'; break
                case 5: tagType = 'error'; break
            }
            return status ? h(NTag, { type: tagType }, { default: () => status.label }) : h('span', '')
        }
    },
    {
        title: '供应商',
        key: 'supplier_name',
        width: 150,
        align: 'center',
        render(row) {
            return h('span', row.supplier_name || '未知供应商')
        }
    },
    {
        title: '订单金额',
        key: 'order_total',
        width: 120,
        align: 'center',
        render(row) {
            return h('span', `¥${row.order_total.toFixed(2)}`)
        }
    },
    {
        title: '创建时间',
        key: 'created_at',
        width: 180,
        align: 'center',
        render(row) {
            return h('span', formatDate(row.created_at))
        }
    },
    {
        title: '操作',
        key: 'actions',
        width: 150,
        align: 'center',
        fixed: 'right',
        render(row) {
            const buttons = []

            // 发货按钮：仅在状态为 待处理(1) 时显示
            if (row.order_status === 1) {
                buttons.push(
                    withDirectives(
                        h(
                            NButton,
                            {
                                size: 'small',
                                type: 'primary',
                                style: 'margin-right: 8px;',
                                onClick: () => {
                                    // 发货逻辑，可能需要调用特定 API 更新状态为 已出库(2)
                                    // 暂时调用 handleEdit，可以修改为 handleShipment 或类似
                                    handleShowShipment(row)
                                },
                            },
                            {
                                default: () => '发货',
                                icon: renderIcon('material-symbols:local-shipping-outline', { size: 16 }),
                            }
                        ),
                        [[vPermission, 'post/api/v1/orders/shipment']] // 使用更新权限或专门的发货权限
                    )
                )
            }

            // 确认支付按钮：仅在状态为 已出库(2) 时显示
            if (row.order_status === 2) {
                buttons.push(
                    withDirectives(
                        h(
                            NButton,
                            {
                                size: 'small',
                                type: 'success', // 使用 success 类型表示支付
                                style: 'margin-right: 8px;',
                                onClick: () => {
                                    // 确认支付逻辑，可能需要调用特定 API 更新状态为 已支付(4)
                                    // 暂时调用 handleEdit，可以修改为 handlePayment 或类似
                                    handleEdit(row) 
                                },
                            },
                            {
                                default: () => '确认支付',
                                icon: renderIcon('material-symbols:payments-outline', { size: 16 }), // 支付图标
                            }
                        ),
                        [[vPermission, 'post/api/v1/orders/pay']] // 假设的支付权限
                    )
                )
            }

            // 查看详情按钮 - 所有状态都可以查看
            buttons.push(
                withDirectives(
                    h(
                        NButton,
                        {
                            size: 'small',
                            type: 'info',
                            style: 'margin-right: 8px;',
                            onClick: () => {
                                handleViewDetail(row)
                            },
                        },
                        {
                            default: () => '详情',
                            icon: renderIcon('material-symbols:visibility-outline', { size: 16 }),
                        }
                    ),
                    [[vPermission, 'get/api/v1/orders/detail']]
                )
            )

            // 总是显示编辑按钮（如果需要根据状态隐藏，可以添加条件）
            // buttons.push(
            //     withDirectives(
            //         h(
            //             NButton,
            //             {
            //                 size: 'small',
            //                 type: 'primary',
            //                 style: 'margin-right: 8px;',
            //                 onClick: () => {
            //                     handleEdit(row)
            //                 },
            //             },
            //             {
            //                 default: () => '编辑', // 如果需要保留编辑按钮
            //                 icon: renderIcon('material-symbols:edit-outline', { size: 16 }),
            //             }
            //         ),
            //         [[vPermission, 'post/api/v1/order/update']]
            //     )
            // )
            
            // 删除按钮（已被注释掉，根据需要恢复）
            // h(
            //     NPopconfirm,
            //     {
            //         onPositiveClick: () => handleDelete({ id: row.id }, false),
            //         onNegativeClick: () => { },
            //     },
            //     {
            //         trigger: () =>
            //             withDirectives(
            //                 h(
            //                     NButton,
            //                     {
            //                         size: 'small',
            //                         type: 'error',
            //                         style: 'margin-right: 8px;',
            //                     },
            //                     {
            //                         default: () => '删除',
            //                         icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
            //                     }
            //                 ),
            //                 [[vPermission, 'delete/api/v1/order/delete']]
            //             ),
            //         default: () => h('div', {}, '确定删除该订单吗?'),
            //     }
            // )

            return buttons
        }
    }
]
</script>

<template>
    <CommonPage show-footer title="订单管理">
        <template #action>
            <NButton v-permission="'post/api/v1/orders/create'" type="primary" @click="handleAdd">
                <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增订单
            </NButton>
        </template>

        <CrudTable ref="$table" v-model:query-items="queryItems" :columns="columns" :get-data="api.getOrderList">
            <template #queryBar>
                <QueryBarItem label="订单编号" :label-width="80">
                    <NInput v-model:value="queryItems.orderNumber" clearable type="text" placeholder="请输入订单编号"
                        @keypress.enter="$table?.handleSearch()" />
                </QueryBarItem>
                <QueryBarItem label="订单类型" :label-width="80">
                    <NSelect v-model:value="queryItems.orderType" :options="orderTypeOptions" clearable
                        placeholder="请选择订单类型" @update:value="$table?.handleSearch()" />
                </QueryBarItem>
                <QueryBarItem label="订单状态" :label-width="80">
                    <NSelect v-model:value="queryItems.orderStatus" :options="orderStatusOptions" clearable
                        placeholder="请选择订单状态" @update:value="$table?.handleSearch()" />
                </QueryBarItem>
                <QueryBarItem v-permission="'post/api/v1/orders/create'" label="供应商" :label-width="80">
                    <NSelect v-model:value="queryItems.supplierId" :options="supplierOptions" clearable
                        placeholder="请选择供应商" @update:value="$table?.handleSearch()" />
                </QueryBarItem>
            </template>
        </CrudTable>

        <!-- 订单食材弹窗 -->
        <NModal v-model:show="modalVisible" preset="card" title="订单食材" style="width: 60%" :mask-closable="true"
            size="huge" :bordered="false">
            <NForm ref="modalFormRef" label-placement="left" label-align="left" :label-width="100" :model="modalForm"
                :rules="formRules" :disabled="modalAction === 'view'">
                <NFormItem label="订单明细" path="orderItems" :label-width="80">
                    <NList bordered clickable hoverable style="width: 100%;">
                        <template #header>
                            <div
                                style="display: grid; grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr 1fr; gap: 10px; font-weight: bold;">
                                <div>名称</div>
                                <div>单位</div>
                                <div>数量</div>
                                <div>单价 (元)</div>
                                <div>生产日期</div>
                                <div>保质期(天)</div>
                            </div>
                        </template>
                        <NListItem v-for="(item, index) in modalForm.orderItems" :key="item.id || index">
                            <div
                                style="display: grid; grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr 1fr; gap: 10px; align-items: center;">
                                <div>{{ item.name }}</div>
                                <div>{{ item.unitName }}</div>
                                <div>{{ item.quantity }}</div>
                                <div>
                                    <NInputNumber v-model:value="item.price" placeholder="请输入单价" :min="0" :precision="2"
                                        :step="0.01" clearable style="width: 100%;" />
                                </div>
                                <div>
                                    <NDatePicker v-model:value="item.production_date" type="date" placeholder="选择生产日期"
                                        clearable style="width: 100%;" />
                                </div>
                                <div>
                                    <NInputNumber v-model:value="item.shelf_life" placeholder="保质期" :min="1" clearable
                                        style="width: 100%;" />
                                </div>
                            </div>
                        </NListItem>
                        <template #footer v-if="!modalForm.orderItems || modalForm.orderItems.length === 0">
                            暂无订单明细
                        </template>
                    </NList>
                </NFormItem>
            </NForm>
            <template #footer>
                <div style="display: flex; justify-content: flex-end">
                    <NSpace>
                        <NButton @click="modalVisible = false">
                            取消
                        </NButton>
                        <NPopconfirm positive-text="确认" negative-text="取消" @positive-click="handleShipment">
                            <template #trigger>
                                <NButton type="primary">
                                    确认发货
                                </NButton>
                            </template>
                            <div>确定要发货吗？</div>
                        </NPopconfirm>
                    </NSpace>
                </div>
            </template>
        </NModal>

        <!-- 订单详情弹窗 -->
        <NModal v-model:show="detailModalVisible" preset="card" title="订单详情" style="width: 70%" :mask-closable="true">
            <div v-if="orderDetail" style="padding: 20px;">
                <!-- 订单基本信息 -->
                <div style="margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #333;">订单基本信息</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <div style="margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #666;">订单编号：</span>
                                <span>{{ orderDetail.order_number }}</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #666;">订单类型：</span>
                                <NTag
                                    :type="orderDetail.order_type === 1 ? 'info' : orderDetail.order_type === 2 ? 'warning' : 'success'">
                                    {{ orderTypeOptions.find(item => item.value === orderDetail.order_type)?.label ||
                                    '未知' }}
                                </NTag>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #666;">订单状态：</span>
                                <NTag
                                    :type="orderDetail.order_status === 1 ? 'warning' : orderDetail.order_status === 2 ? 'info' : orderDetail.order_status === 3 || orderDetail.order_status === 4 ? 'success' : 'error'">
                                    {{ orderStatusOptions.find(item => item.value === orderDetail.order_status)?.label
                                    || '未知' }}
                                </NTag>
                            </div>
                        </div>
                        <div>
                            <div style="margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #666;">订单金额：</span>
                                <span style="color: #f56c6c; font-size: 16px; font-weight: bold;">¥{{
                                    orderDetail.order_total?.toFixed(2) || '0.00' }}</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <span style="font-weight: bold; color: #666;">创建时间：</span>
                                <span>{{ formatDate(orderDetail.created_at) }}</span>
                            </div>
                            <div style="margin-bottom: 10px;" v-if="orderDetail.updated_at">
                                <span style="font-weight: bold; color: #666;">更新时间：</span>
                                <span>{{ formatDate(orderDetail.updated_at) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单明细 -->
                <div>
                    <h3 style="margin-bottom: 20px; color: #333;">订单明细</h3>
                    <div style="border: 1px solid #eee; border-radius: 6px; overflow: hidden;">
                        <div
                            style="display: grid; grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr 1.5fr 1fr; gap: 10px; font-weight: bold; background-color: #f5f5f5; padding: 15px; border-bottom: 1px solid #eee;">
                            <div>食材名称</div>
                            <div>单位</div>
                            <div>数量</div>
                            <div>单价 (元)</div>
                            <div>小计 (元)</div>
                            <div>生产日期</div>
                            <div>保质期(天)</div>
                        </div>
                        <div v-if="orderDetail.order_items && orderDetail.order_items.length > 0">
                            <div v-for="(item, index) in orderDetail.order_items" :key="index"
                                style="display: grid; grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr 1.5fr 1fr; gap: 10px; align-items: center; padding: 15px; border-bottom: 1px solid #f0f0f0;">
                                <div style="font-weight: 500;">{{ item.name }}</div>
                                <div>{{ item.unitName }}</div>
                                <div>{{ item.quantity }}</div>
                                <div>{{ item.price?.toFixed(2) || '0.00' }}</div>
                                <div style="color: #f56c6c; font-weight: bold;">{{ ((item.price || 0) * (item.quantity
                                    || 0)).toFixed(2) }}</div>
                                <div>{{ item.production_date ? new Date(item.production_date).toLocaleDateString() : '-'
                                    }}</div>
                                <div>{{ item.shelf_life || '-' }}</div>
                            </div>
                        </div>
                        <div v-else style="text-align: center; padding: 40px; color: #999;">
                            暂无订单明细
                        </div>
                    </div>
                </div>
            </div>
            <div v-else style="text-align: center; padding: 40px;">
                <span style="color: #999;">加载中...</span>
            </div>
            <template #footer>
                <div style="display: flex; justify-content: flex-end">
                    <NButton @click="detailModalVisible = false">
                        关闭
                    </NButton>
                </div>
            </template>
        </NModal>

        <!-- 打印预览弹窗 -->
        <NModal v-model:show="printModalVisible" preset="card" title="打印预览" style="width: 80%" :mask-closable="false">
            <div id="printArea" style="padding: 20px;">
                <!-- 标题和二维码区域 -->
                <div
                    style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 30px;">
                    <div style="flex: 1;"></div>
                    <div style="flex: 2; text-align: center;">
                        <h1 style="font-size: 24px; margin-bottom: 10px;">{{ supplierInfo?.name || currentOrder?.supplier_name || '供应商' }}食材供货单</h1>
                        <div v-if="supplierInfo?.phone" style="font-size: 16px; color: #666; margin-bottom: 20px;">
                            联系电话：{{ supplierInfo.phone }}
                        </div>
                    </div>
                    <div style="flex: 1; text-align: right;">
                        <!-- <NQrCode v-if="currentOrder" :value="String(currentOrder.id)" :size="100" /> -->
                        <QrcodeVue v-if="currentOrder" :value="String(currentOrder.id)" :size="100" />
                        <div v-if="currentOrder" style="margin-top: 5px; font-size: 12px;">
                            <div>订单编号: {{ currentOrder.order_number }}</div>
                            <div>发货日期: {{ new Date().toLocaleDateString('zh-CN') }}</div>
                        </div>
                    </div>
                </div>

                <!-- 食材表格 -->
                <div style="margin-top: 20px;">
                    <div
                        style="display: grid; grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 1.5fr 1fr; gap: 10px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                        <div>名称</div>
                        <div>单位</div>
                        <div>数量</div>
                        <div>单价 (元)</div>
                        <div>小计 (元)</div>
                        <div>生产日期</div>
                        <div>保质期(天)</div>
                    </div>
                    <div v-if="currentOrder?.order_items">
                        <div v-for="(item, index) in currentOrder.order_items" :key="index"
                            style="display: grid; grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 1.5fr 1fr; gap: 10px; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee;">
                            <div>{{ item.name }}</div>
                            <div>{{ item.unitName }}</div>
                            <div>{{ item.quantity }}</div>
                            <div>{{ item.price?.toFixed(2) || '0.00' }}</div>
                            <div style="color: #f56c6c; font-weight: bold;">{{ ((item.price || 0) * (item.quantity ||
                                0)).toFixed(2) }}</div>
                            <div>{{ item.production_date ? new Date(item.production_date).toLocaleDateString() : '-' }}
                            </div>
                            <div>{{ item.shelf_life || '-' }}</div>
                        </div>
                    </div>
                    <div v-else style="text-align: center; padding: 20px;">
                        暂无订单明细
                    </div>
                </div>
            </div>
            <template #footer>
                <div style="display: flex; justify-content: flex-end">
                    <NSpace>
                        <NButton @click="printModalVisible = false">
                            取消
                        </NButton>
                        <NButton type="primary" @click="printOrder">
                            确认打印
                        </NButton>
                    </NSpace>
                </div>
            </template>
        </NModal>
    </CommonPage>
</template>