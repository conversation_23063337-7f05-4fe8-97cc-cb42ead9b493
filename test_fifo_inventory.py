#!/usr/bin/env python3
"""
测试先进先出库存扣除逻辑
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from app.models.food_stuff import FoodStuffStore, FoodStuff
from app.controllers.order import OrderController


async def setup_test_data():
    """设置测试数据"""
    print("设置测试数据...")
    
    # 创建测试食材
    food_stuff = await FoodStuff.create(
        food_stuff_name="测试大米",
        food_stuff_type=1,
        food_stuff_unit_id=1,
        supplier_id=1
    )
    
    # 创建多个库存记录，模拟不同时间的入库
    base_time = datetime.now() - timedelta(days=5)
    
    # 第一批入库：100kg，5天前
    store1 = await FoodStuffStore.create(
        food_stuff_id=food_stuff.id,
        store_count=100.0,
        school_id=1,
        supplier_id=1,
        order_id=1,
        recipe_id=None,
        created_at=base_time
    )
    
    # 第二批入库：50kg，3天前
    store2 = await FoodStuffStore.create(
        food_stuff_id=food_stuff.id,
        store_count=50.0,
        school_id=1,
        supplier_id=1,
        order_id=2,
        recipe_id=None,
        created_at=base_time + timedelta(days=2)
    )
    
    # 第三批入库：80kg，1天前
    store3 = await FoodStuffStore.create(
        food_stuff_id=food_stuff.id,
        store_count=80.0,
        school_id=1,
        supplier_id=1,
        order_id=3,
        recipe_id=None,
        created_at=base_time + timedelta(days=4)
    )
    
    print(f"创建了食材: {food_stuff.food_stuff_name}")
    print(f"库存记录1: {store1.store_count}kg (订单{store1.order_id}, {store1.created_at})")
    print(f"库存记录2: {store2.store_count}kg (订单{store2.order_id}, {store2.created_at})")
    print(f"库存记录3: {store3.store_count}kg (订单{store3.order_id}, {store3.created_at})")
    print(f"总库存: {100 + 50 + 80}kg")
    
    return food_stuff.id


async def test_fifo_deduction(food_stuff_id: int):
    """测试先进先出扣除逻辑"""
    print("\n开始测试先进先出扣除逻辑...")
    
    controller = OrderController()
    
    # 测试1: 扣除120kg（应该先扣除第一批100kg，再扣除第二批20kg）
    print("\n测试1: 扣除120kg")
    result = await controller._deduct_inventory_fifo(
        food_stuff_id=food_stuff_id,
        school_id=1,
        supplier_id=1,
        required_quantity=120.0
    )
    
    print(f"剩余需要下单数量: {result['remaining_quantity']}")
    print(f"库存变化记录数: {len(result['inventory_changes'])}")
    
    for change in result['inventory_changes']:
        print(f"  - 扣除前: {change['before_count']}kg, 扣除: {change['change_count']}kg")
    
    # 检查剩余库存
    remaining_stores = await FoodStuffStore.filter(
        food_stuff_id=food_stuff_id,
        school_id=1,
        supplier_id=1
    ).order_by('created_at')
    
    print(f"剩余库存记录数: {len(remaining_stores)}")
    for store in remaining_stores:
        print(f"  - 订单{store.order_id}: {store.store_count}kg")


async def main():
    """主函数"""
    # 初始化数据库连接
    await Tortoise.init(
        db_url="sqlite://test_fifo.db",
        modules={"models": ["app.models.food_stuff", "app.models.orders", "app.models.admin"]}
    )
    
    # 生成数据库表
    await Tortoise.generate_schemas()
    
    try:
        # 设置测试数据
        food_stuff_id = await setup_test_data()
        
        # 测试先进先出扣除
        await test_fifo_deduction(food_stuff_id)
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
